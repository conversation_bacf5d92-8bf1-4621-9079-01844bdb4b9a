class MapInternalCustId {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.internalCustIdLib = options.internalCustIdLib;
    }

    getPayloadWithInternalCustId(record) {
        let self = this;
        let payload = _.cloneDeep(record);
        console.log("🚀 ~ MapInternalCustId ~ getPayloadWithInternalCustId ~ this.internalCustIdLib:", this.internalCustIdLib)
        return payload;
    }
}

export default {
    MapInternalCustId
};