import Q from 'q'
import _ from 'lodash'
import Bills from '../models/bills'

class LoadInternalCustIdLib {

    constructor(options) {
        this.refreshInterval = 24 * 60 * 60 * 1000;
        this.L = options.L;
        this.config = options.config;
        this.bills = new Bills(options);
    }

    load() {
        let self = this;
        let deferred = Q.defer();
        self.getInternalCustId(function (err, internalCustIdMap) {
            if (err) {
                deferred.reject(err);
            }
            _.extend(self.config.INTERNAL_CUST_ID_MAP, internalCustIdMap);
            setInterval(function () {
                self.getInternalCustId(function (err, internalCustIdMap) {
                    if (err) {
                        self.L.critical(`Unable to parse dynamic config: ${err}`);
                    } else {
                        _.extend(self.config.INTERNAL_CUST_ID_MAP, internalCustIdMap);
                    }
                });
            }, self.refreshInterval);
            deferred.resolve();
        });

        return deferred.promise;
    }

    getInternalCustId(cb) {
        let self = this;
        self.bills.getInternalCustId(function (err, data) {
            if (err) {
                self.L.error("Error in getting internal cust id", err);
                return cb(err);
            } else {
                self.L.verbose("Internal cust id fetched successfully", data);
                //map the custId to recharge_number and service and paytype and operator
                let internalCustIdMap = {};
                data.forEach(item => {
                    //recharge_number_service_paytype_operator vs customer_id
                    internalCustIdMap[item.recharge_number + "_" + item.service + "_" + item.paytype + "_" + item.operator] = {
                        internalCustId: item.customer_id
                    };
                });
                self.L.verbose("Internal cust id map", internalCustIdMap);
                return cb(null, internalCustIdMap);
            }
            return cb(null, data);
        });
    }
}

export default LoadInternalCustIdLib;