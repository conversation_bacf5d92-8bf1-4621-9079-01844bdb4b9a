import _ 		from 'lodash'
import utility 	from './datadog'
import MOMENT 	from 'moment'

import AES256Encryption from '../utils/aes256encryption'

class EncryptionDecryptioinHelper {
    
	constructor(options) {
		
		this.L = options.L;
		this.config = options.config;
		this.aes256Encryption = new AES256Encryption(options);
		this.fetchWhitelistedCustIdConfig();
	}

	/*
	 * dbResponse: array of objects
	 * cust_id: number
	 * parsingStructure: 
							{
								"extra.minDueAmount": {
									"dataType": "date",
									"dateFormat": "YYYY-MM-DD"
								},
								"amount": {
									"dataType": "float"
								}
							}
	*/
	parseDbResponse(dbResponse, cust_id, parsingStructure = null) {

		let self = this;
		if (_.isEmpty(dbResponse) || cust_id == null) {
			return dbResponse;
		}

		if(parsingStructure == null) parsingStructure = self.ccDbStructure;

		if (dbResponse.length > 0) {
			for(let index = 0; index < dbResponse.length; index++) {
			
				let dbRecord = dbResponse[index];

				if(
					!self.isWhiteListedCustId(cust_id) ||
					_.get(dbRecord,'is_encrypted',false) != true ||
					parsingStructure == null	
				) continue;

				let parsingStructureKeys = Object.keys(parsingStructure);
				for(let parsingStructureIndex in parsingStructureKeys) {
					
					let keyName = parsingStructureKeys[parsingStructureIndex];

					if(_.get(parsingStructure[keyName],'dataType',null) == null) continue;

					let parsingParams = keyName.split(".");
					
					// if(parsingParams.length == 0) continue;
					
					let dbColumn = parsingParams[0];

					if(typeof dbRecord[dbColumn] == "string") dbRecord[dbColumn] = (dbRecord[dbColumn]).trim();
					if(dbRecord[dbColumn] == null || dbRecord[dbColumn] == "" || dbRecord[dbColumn] == undefined) continue;

					if(parsingParams.length == 1) {
						let decryptedValue;
						try {
							decryptedValue = self.parseValue(
								self.decryptData(_.get(dbRecord,parsingParams,null)), 
								_.get(parsingStructure[keyName],'dataType',null), 
								_.get(parsingStructure[keyName],'dateFormat',null)
							);
						} catch(e) {
							self.L.error(`parseDbResponse :: Error while decrypting ${_.get(dbRecord,parsingParams,null)}. Error:: `, e);
							dbRecord[dbColumn] = _.get(dbRecord,parsingParams,null);
							continue;
						}
						
						dbRecord[dbColumn] = decryptedValue;
						if(_.get(parsingStructure[keyName],'targetKey',null) != null) {
							self.processTargetKeys(dbRecord, _.get(parsingStructure[keyName],'targetKey',null), decryptedValue);
						}
					} else if(parsingParams.length > 1) {
						try {
							dbRecord[dbColumn] = JSON.parse(dbRecord[dbColumn]);
						} catch(e) {
							self.L.error(`parseDbResponse :: Error while parsing ${dbRecord[dbColumn]}. Error:: `, e);
							continue;
						}
						if(_.get(dbRecord,parsingParams,null) != null && _.get(dbRecord,parsingParams,null) != "") {
							let decryptedValue;
							try {
								decryptedValue = self.parseValue (
									self.decryptData(_.get(dbRecord,parsingParams,null)), 
									_.get(parsingStructure[keyName],'dataType',null), 
									_.get(parsingStructure[keyName],'dateFormat',null)
								);
							} catch(e) {
								self.L.error(`parseDbResponse :: Error while decrypting ${_.get(dbRecord,parsingParams,null)}. Error:: `, e);
								dbRecord[dbColumn] = _.get(dbRecord,parsingParams,null);
								continue;
							}
							_.set(dbRecord,parsingParams, decryptedValue);
							if(_.get(parsingStructure[keyName],'targetKey',null) != null) self.processTargetKeys(dbRecord, _.get(parsingStructure[keyName],'targetKey',null), decryptedValue);
						}
						dbRecord[dbColumn] = JSON.stringify(dbRecord[dbColumn]);
					}
				}
				
			}
		}
		return dbResponse;
	}

	processTargetKeys(dbRecord, targetKey_s, decryptedValue) {
		let self = this;
		if(Array.isArray(targetKey_s)) {
			for(let index = 0; index < targetKey_s.length; index++) {
				self.processTargetKey(dbRecord, targetKey_s[index], decryptedValue);
			}
		} else if (typeof targetKey_s == 'string') {
			self.processTargetKey(dbRecord, targetKey_s, decryptedValue);
		}
	}

	processTargetKey(dbRecord, targetKey, decryptedValue) {
		let self = this,
			valueParsed = false,
			targetKeyStructure = targetKey.split(".");
		if(targetKeyStructure.length > 1) {
			try {
				if(typeof dbRecord[targetKeyStructure[0]] == "string") {
					dbRecord[targetKeyStructure[0]] = JSON.parse(dbRecord[targetKeyStructure[0]]);
					valueParsed = true;
				}
			} catch(e) {
				self.L.error(`processTargetKey :: Error while parsing ${dbRecord[targetKeyStructure[0]]}. Error:: `, e);
				return;
			}
			if(targetKey == 'due_amount' && _.has(dbRecord,'due_amount') == false) {
				// console.log("1 .due_amount is undefined");
			} 
			else {
				_.set(dbRecord,targetKey, decryptedValue);
			}
			if(valueParsed == true) dbRecord[targetKeyStructure[0]] = JSON.stringify(dbRecord[targetKeyStructure[0]]);
		} else {
			if(targetKey == 'due_amount' && _.has(dbRecord,'due_amount') == false) {
				// console.log("2 .due_amount is undefined");
			} else {
				_.set(dbRecord,targetKey, decryptedValue);
			}
		}
	}

	isWhitelistedForCC(service, paytype, custId) {
        let self = this;
		if(service == null || service == undefined) service = '';
		if(paytype == null || paytype == undefined) paytype = '';
		if(self.IS_CCBP_ENC_ENABLED == true && service.toLowerCase() == "financial services" && paytype.toLowerCase() == "credit card" && this.isWhiteListedCustId(custId)) return true;
        return false;

    }

	/* 
	 * custId: number
	 * checks if cust_id is in CUG list or in allowed percentage range
	*/
	isWhiteListedCustId(custId) {

		if(custId == null || custId == undefined || isNaN(Number(custId))) return false;
		
		let self = this;
		if(custId%100 <= self.allowedRolloutPercentage) {
			return true;
		} else if(self.cugList.has(Number(custId))) {
			return true;
		}
		return false;
	}

	// expects string as an input for encryption
	encryptData(data) {
		
		let self = this;
		if (_.isEmpty(data)) {
			return null;
		} else if (!_.isString(data)) {
			throw new Error("encryptData :: Invalid input data");
		}
		return self.aes256Encryption.encrypt(data);
	}

	// expects string as an input for decryption
	decryptData(encryptedString) {
		
		let self = this;
		if (_.isEmpty(encryptedString)) {
			return null;
		} else if (!_.isString(encryptedString)) {
			throw new Error("decryptData :: Invalid input data");
		}
		return self.aes256Encryption.decrypt(encryptedString);
	}

	fetchWhitelistedCustIdConfig() {
		
		let self = this;

		//fetch allowedRolloutPercentage
		self.allowedRolloutPercentage = _.get(self.config, ['DYNAMIC_CONFIG', 'CCBP', 'ENCRYPTION_CONFIG', 'ROLLOUT_PERCENTAGE'], null);
		self.ccDbStructure = _.get(self.config, ['DYNAMIC_CONFIG', 'CCBP', 'ENCRYPTION_CONFIG','DB_STRUCTURE'], null);
		self.IS_CCBP_ENC_ENABLED = _.get(self.config, ['DYNAMIC_CONFIG', 'CCBP', 'ENCRYPTION_CONFIG', 'IS_CCBP_ENC_ENABLED'], false);

		self.keysToEncryptForCustomerOtherInfo = _.get(this.config, ['DYNAMIC_CONFIG', 'CCBP', 'ENCRYPTION_CONFIG', 'CUSTOMER_OTHER_INFO_KEYS'], ['subscriberNumber', 'subscriberName', 'subscriberEmailId', 'subscriberDOB', 'subscriberAddress', 'subscriberAltNumber', 'subscriberGender', 'subscriberCity', 'minReloadAmount', 'currentBillAmount', 'currentMinBillAmount', 'billDueDate', 'billDueAmount', 'lastCC', 'rawLastCC', 'debugKey', 'currentOutstandingAmount']);
        self.keysToEncryptForCustomerExtra = _.get(this.config, ['DYNAMIC_CONFIG', 'CCBP', 'ENCRYPTION_CONFIG', 'EXTRA_KEYS'], ['subscriberNumber', 'subscriberName', 'subscriberEmailId', 'subscriberDOB', 'subscriberAddress', 'subscriberAltNumber', 'subscriberGender', 'subscriberCity', 'minReloadAmount', 'currentBillAmount', 'currentMinBillAmount', 'billDueDate', 'billDueAmount', 'lastCC', 'rawLastCC', 'debugKey', 'currentOutstandingAmount', 'lastDueDt', 'lastAmount']);
		self.keysToEncryptForUserData = _.get(this.config, ['DYNAMIC_CONFIG', 'CCBP', 'ENCRYPTION_CONFIG', 'USER_DATA_KEYS'], ['recharge_number_2', 'recharge_number_3', 'recharge_number_4', 'recharge_number_5', 'recharge_number_6']);

		// if allowedRolloutPercentage is 100, no need to fetch and update CUG list and allow percentage config
		if(self.allowedRolloutPercentage == 100) return;
		
		// fetch CUG list
		let cugList = _.get(self.config, ['DYNAMIC_CONFIG', 'CCBP', 'ENCRYPTION_CONFIG','ALLOEWD_ROLLOUT_CUG_LIST'], []);
		self.cugList = new Set(cugList); //converting to set for faster lookup

		// updating self.allowedRolloutPercentage and self.cugList every 15 minutes
		setTimeout(() => {
			self.fetchWhitelistedCustIdConfig();
		}, 1000 * 60 * 15);
	}

	parseValue(data, dataType, dateFormat) {
		if(data == null) return null;
		let self = this;
		if(dataType == "string") {
			if(typeof data == "string") return data;
			return self.parseToString(data);
		} else if(dataType == "float") {
			return self.parseToFloat(data);
		} else if(dataType == "date") {
			return self.parseDate(data, dateFormat);
		}
		throw new Error("parseValue :: Invalid dataType");
	}

	// expects string as an input and returns integer/float
	parseToFloat(data) {
		if(data == null) return null;
		let self = this;
		try {
			let parsedValue = parseFloat(data);
			if (isNaN(parsedValue)) {
				throw new Error("Invalid input data");
			}
			return parsedValue;
		} catch (e) {
			self.L.error("Error in parseToFloat", e);
			return null;
		}
	}

	/*
	 * date: string
	 * dateFormat: string
	 * returns moment object
	*/
	parseDate(date, dateFormat) {

		if(date == null) return null;
		let self = this;
		try {
			let parsedDate;
			if(!dateFormat) {
				parsedDate = MOMENT(date);
			} else {
				parsedDate = MOMENT(date, dateFormat);
			}
			return parsedDate;
		} catch (e) {
			self.L.error("Error in parseDate", e);
			return null;
		}
	}

	// converts input data to string
	parseToString(data) {

		if(data == null) return null;
		let self = this;
		try {
			return JSON.stringify(data);
		} catch (e) {
			self.L.error("Error in toString", e);
			return null;
		}
	}

	//function to encrypt fields in json , only 1 level of json is supported, no nested jsons are supported
	encryptJson(jsonData, keysToEncrypt){
		let self = this;
		let jsonPassedAsStr = false;
		if(_.isEmpty(jsonData) || _.isEmpty(keysToEncrypt)) return jsonData;
		if(typeof jsonData == 'string') {
			jsonData = JSON.parse(jsonData);
			jsonPassedAsStr = true;
		}
		for(let index in keysToEncrypt){
			let key = keysToEncrypt[index];
			if(_.get(jsonData,key,null) != null){
				if(typeof _.get(jsonData,key) != 'string') jsonData[key] = self.parseToString(_.get(jsonData,key));
				_.set(jsonData,key,self.encryptData(_.get(jsonData,key)));
			}
		}
		if(jsonPassedAsStr) return JSON.stringify(jsonData);
		return jsonData;
	}

	getEncryptedParamsFromGenericParams(params, rootLevelKeysToEncrypt = null, keysForCustomerExtra = null, keysForCustomerOtherInfo = null, keysForUserData = null) {
        let self = this;
		let cust_id = _.get(params, "customerId", _.get(params, "customer_id", null));
		if(!params || !params.service || (!cust_id) || !params.paytype) {
			return params;
		}
		
		let isCCEncryptionEnabled = self.isWhitelistedForCC(params.service, params.paytype, cust_id);
		if(!isCCEncryptionEnabled) {
            return params;
        }
		if(!keysForCustomerExtra) {
			keysForCustomerExtra = self.keysToEncryptForCustomerExtra;
		}
		if(!keysForCustomerOtherInfo) {
			keysForCustomerOtherInfo = self.keysToEncryptForCustomerOtherInfo;
		}
		if(!keysForUserData) {
			keysForUserData = self.keysToEncryptForUserData;
		}
        let encrypted_params = _.cloneDeep(params);
		if(!rootLevelKeysToEncrypt) {
			rootLevelKeysToEncrypt = ['rechargeNumber', 'reference_id', 'customer_mobile', 'customer_email'];
		}
        encrypted_params = self.encryptJson(encrypted_params, rootLevelKeysToEncrypt);
        if(_.get(params,'amount', null) != null) _.set(encrypted_params, 'enc_amount', self.encryptData(_.toString(_.get(params, 'amount', ''))));
		if(_.get(params,'due_amount', null) != null) _.set(encrypted_params, 'enc_amount', self.encryptData(_.toString(_.get(params, 'due_amount', ''))));
        if(_.get(params,'due_date', null) != null) _.set(encrypted_params, 'enc_due_date', self.encryptData(_.get(params, 'due_date', '')));
        _.set(encrypted_params, 'extra', self.encryptJson(_.get(params, 'extra', ''), keysForCustomerExtra));
        if(params.customerOtherInfo) _.set(encrypted_params, 'customerOtherInfo', self.encryptJson(_.get(params, 'customerOtherInfo', '') , keysForCustomerOtherInfo));
		if(params.customer_other_info) _.set(encrypted_params, 'customer_other_info', self.encryptJson(_.get(params, 'customer_other_info', '') , keysForCustomerOtherInfo));
        _.set(encrypted_params, 'user_data', self.encryptJson(_.get(params, 'user_data', ''), keysForUserData));
        _.set(encrypted_params, 'is_encrypted', 1);
        return encrypted_params;
    }

	getNotificationParamsForCCBP(notifier) {
		let params = {
            paytype: _.toLower(_.get(notifier, 'data.dynamicParams.paytype', _.get(notifier, 'data.options.data.paytype', null))),
            service: _.toLower(_.get(notifier, 'data.dynamicParams.service', _.get(notifier, 'data.options.data.service', null))),
            customerId: _.get(notifier, 'data.dynamicParams.customer_id', _.get(notifier, 'data.options.data.customer_id', null))
        };
		return params;
    }

}

export default EncryptionDecryptioinHelper;