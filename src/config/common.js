export default {
    common: {
        CONSENT_REQUIRED: 2,
        NOT_REMINDABLE: 0,
        NO_OF_DAYS_TO_STOP_BILLS_FROM_PUBLISHING: 60,
        MAX_VALID_DATE: '2038-01-19',
        bills_status: {
            PENDING: 0,
            PUBLISHED: 1,
            RETRY: 2,
            MAX_RETRY_REACHED: 3,
            B<PERSON><PERSON>_FETCHED: 4,
            OLD_BILL_FOUND: 5,
            BILL_NOT_FOUND: 6,
            DISABLED: 7,
            CONNECTION_ERROR: 8,
            VALIDATION_FAILED: 9,
            WRONG_DUE_DATE: 10,
            PAYMENT_DONE: 11,
            ERROR_WHILE_PUBLISHING: 12,
            NOT_IN_USE: 13,
            PAID_ON_OTHER_PLATFORM: 14,
            MARKED_AS_PAID: 15,
            EARLY_BILL_FETCH : 16,
            SKIP_CC_BILL : 17,
            // Airtel Prepaid specific error codes
            NOT_IN_USE_INVALID_RECHARGE_NUMBER : 131,  // using 13* series for invalid/not in use/removed records
            NOT_IN_USE_AFTER_GENERIC_NOTI : 132,
            NOT_IN_USE_AFTER_GENERIC_NOTI_AFTER_ERR : 133,
            NOT_IN_USE_AFTER_LAST_NOTI : 134,
            NOT_IN_USE_PAID_ON_OTHER_PLATFORM : 135,
            NOT_IN_USE_ERR_BILL_FETCH : 136,
            NOT_IN_USE_PAID_ON_PAYTM : 137,
            NOT_IN_USE_BLACKLISTED: 138,
        },
        BILL_PUSH_ERROR_CODE: {
            'INVALID_BILL_INFO': {
                status: '01', message: 'Invalid bill info'
            },
            'INTERNAL_SYSTEM_ERROR': {
                status: '03', message: 'Internal system error'
            },
            'MANDATORY_PARAM_MISSING': {
                status: '01', message: 'mandatory params missing'
            }
        },
        notification_status: {
            DISABLED: 0,
            ENABLED: 1,
            DISABLED_TEMP: 2 // Disabled until next transaction
        },
        MIN_NOTIFY_AMOUNT: 0,
        operatorNthPrimaryCycle: {
            'bses': 3
        },
        GET_MULTIPLE_BILL_LIMIT: 500,
        CALLBACK_STATUS_CODES: {
            SUCCESS: 0,
            FAILURE: 1,
            UNKNOWN_ERROR: 2
        },
        NOTIFICATION_STATUS_MAP: {
            0: 'unsubscribed',
            1: 'subscribed'
        },
        NON_PAYTM_CC: {
            tableName: 'bills_nonpaytm_creditcard',
            cardScheme: 'dummyNetwork'
        },
        PREPAID_PRODUCT_ID: [
            *********,       //uttar pradesh power corporation ltd. (uppcl)
            92897779         //noida power company ltd (npcl)
        ],
        EMI_DUE_CONSUMER_CONFIG : {
            common : {
                "BLACKLIST_EMI_DUE_OPERATORS"  : {
                    /** 'aditya birla finance limited' : 1 */
                }
            },
            production: {
                'Hero FinCorp' : {
                    product_id : *********,
                    gateway : 'herofincorpapi',
                    categoryName : 'Loan Repayment',
                    operator : 'Hero FinCorp',
                    notificationCreateSendMinDueAmount : 1,
                    time_interval: 360
                },
                'Aditya Birla Finance Limited' : {
                    product_id : *********,
                    gateway : 'bharatbillpay',
                    operator : 'Aditya Birla Finance Limited',
                    notificationCreateSendMinDueAmount : 1,
                    time_interval: 360,
                    mapper_table_column_with_kafka : {
                        "customerId" :  {
                            "kafkaKey" : "cust_id",
                            "mandatoryParams" : true,
                            "numeric" : true
                        },
                        "rechargeNumber" : {
                            "kafkaKey" : "loan_agreement_no",
                            "mandatoryParams" : true
                        },
                        "amount" : {
                            "kafkaKey" : "emi_amount",
                            "mandatoryParams" : true,
                            "parseAmount" : true
                        },
                        "customerEmail" : {
                            "kafkaKey" : "cust_mailing_add_email"
                        },
                        "customerMobile" : {
                            "kafkaKey" : "customer_add_mobile"
                        },
                        "recharge_number_2" : {
                            "kafkaKey" : "customer_add_mobile",
                            "mandatoryParams" : true
                        }
                        // "currentMinBillAmount" : {
                        //     "kafkaKey" : "emi_amount",
                        //     "parseAmount" : true
                        // },
                    }
                },
                'Fullerton India credit company limited' : {
                    product_id : *********,
                    gateway : 'bharatbillpay',
                    operator : 'Fullerton India credit company limited',
                    notificationCreateSendMinDueAmount : 1,
                    time_interval: 360,
                    mapper_table_column_with_kafka : {
                        "customerId" :  {
                            "kafkaKey" : "cust_id",
                            "mandatoryParams" : true,
                            "numeric" : true
                        },
                        "rechargeNumber" : {
                            "kafkaKey" : "loan_agreement_no",
                            "mandatoryParams" : true
                        },
                        "amount" : {
                            "kafkaKey" : "emi_amount",
                            "mandatoryParams" : true,
                            "parseAmount" : true
                        },
                        "customerEmail" : {
                            "kafkaKey" : "cust_mailing_add_email"
                        },
                        "customerMobile" : {
                            "kafkaKey" : "customer_add_mobile"
                        }
                        // "currentMinBillAmount" : {
                        //     "kafkaKey" : "loan_amount",
                        //     "parseAmount" : true
                        // }
                    }
                },
                'Fullerton India Housing Finance Limited' : {
                    product_id : 330778558,
                    gateway : 'bharatbillpay',
                    operator : 'Fullerton India Housing Finance Limited',
                    notificationCreateSendMinDueAmount : 1,
                    time_interval: 360,
                    mapper_table_column_with_kafka : {
                        "customerId" :  {
                            "kafkaKey" : "cust_id",
                            "mandatoryParams" : true,
                            "numeric" : true
                        },
                        "rechargeNumber" : {
                            "kafkaKey" : "loan_agreement_no",
                            "mandatoryParams" : true
                        },
                        "amount" : {
                            "kafkaKey" : "emi_amount",
                            "mandatoryParams" : true,
                            "parseAmount" : true
                        },
                        "customerEmail" : {
                            "kafkaKey" : "cust_mailing_add_email"
                        },
                        "customerMobile" : {
                            "kafkaKey" : "customer_add_mobile"
                        }
                        // "currentMinBillAmount" : {
                        //     "kafkaKey" : "loan_amount",
                        //     "parseAmount" : true
                        // }
                    }
                },
                'Clix' : {
                    product_id : 322580635,
                    gateway : 'bharatbillpay',
                    operator : 'Clix',
                    notificationCreateSendMinDueAmount : 1,
                    time_interval: 360,
                    mapper_table_column_with_kafka : {
                        "customerId" :  {
                            "kafkaKey" : "cust_id",
                            "mandatoryParams" : true,
                            "numeric" : true
                        },
                        "rechargeNumber" : {
                            "kafkaKey" : "loan_agreement_no",
                            "mandatoryParams" : true
                        },
                        "amount" : {
                            "kafkaKey" : "emi_amount",
                            "mandatoryParams" : true,
                            "parseAmount" : true
                        },
                        "customerEmail" : {
                            "kafkaKey" : "cust_mailing_add_email"
                        },
                        "customerMobile" : {
                            "kafkaKey" : "customer_add_mobile"
                        }
                        // "currentMinBillAmount" : {
                        //     "kafkaKey" : "loan_amount",
                        //     "parseAmount" : true
                        // }
                    }
                },
                'StashFin-CAAS' : {
                    product_id : 336587920,
                    gateway : 'bharatbillpay',
                    operator : 'StashFin',
                    notificationCreateSendMinDueAmount : 1,
                    time_interval: 360,
                    mapper_table_column_with_kafka : {
                        "customerId" :  {
                            "kafkaKey" : "cust_id",
                            "mandatoryParams" : true,
                            "numeric" : true
                        },
                        "rechargeNumber" : {
                            "kafkaKey" : "customer_add_mobile",
                            "mandatoryParams" : true
                        },
                        "amount" : {
                            "kafkaKey" : "emi_amount",
                            "mandatoryParams" : true,
                            "parseAmount" : true
                        },
                        "customerEmail" : {
                            "kafkaKey" : "cust_mailing_add_email"
                        },
                        "customerMobile" : {
                            "kafkaKey" : "customer_add_mobile"
                        },
                        "recharge_number_2" : {
                            "kafkaKey" : "DOB", /** "DD-MM-YYYY" --> "28-08-1982" */
                            "mandatoryParams" : true,
                            "parseDate" : true
                        }
                        // "currentMinBillAmount" : {
                        //     "kafkaKey" : "loan_amount",
                        //     "parseAmount" : true
                        // }
                    }
                },
                'L&T Finance Limited-CAAS' : {
                    product_id : 253861270,
                    gateway : 'lntfinance',
                    operator : 'L&T Finance Limited',
                    notificationCreateSendMinDueAmount : 1,
                    time_interval: 360,
                    mapper_table_column_with_kafka : {
                        "customerId" :  {
                            "kafkaKey" : "cust_id",
                            "mandatoryParams" : true,
                            "numeric" : true
                        },
                        "rechargeNumber" : {
                            "kafkaKey" : "loan_agreement_no",
                            "mandatoryParams" : true
                        },
                        "amount" : {
                            "kafkaKey" : "emi_amount",
                            "mandatoryParams" : true,
                            "parseAmount" : true
                        },
                        "customerEmail" : {
                            "kafkaKey" : "cust_mailing_add_email"
                        },
                        "customerMobile" : {
                            "kafkaKey" : "customer_add_mobile"
                        },
                        "recharge_number_2" : {
                            "kafkaKey" : "customer_add_mobile",
                            "mandatoryParams" : true
                        }
                        // "currentMinBillAmount" : {
                        //     "kafkaKey" : "emi_amount",
                        //     "parseAmount" : true
                        // },
                    }
                }
            },
            development: {
                'Hero FinCorp' : {
                    product_id : *********,
                    gateway : 'herofincorpapi',
                    categoryName : 'Loan Repayment',
                    operator : 'Hero FinCorp',
                    notificationCreateSendMinDueAmount : 1,
                    time_interval: 2
                },
                'Aditya Birla Finance Limited' : {
                    product_id : *********,
                    gateway : 'bharatbillpay',
                    operator : 'Aditya Birla Finance Limited',
                    notificationCreateSendMinDueAmount : 1,
                    time_interval: 360,
                    mapper_table_column_with_kafka : {
                        "customerId" :  {
                            "kafkaKey" : "cust_id",
                            "mandatoryParams" : true,
                            "numeric" : true
                        },
                        "rechargeNumber" : {
                            "kafkaKey" : "loan_agreement_no",
                            "mandatoryParams" : true
                        },
                        "amount" : {
                            "kafkaKey" : "emi_amount",
                            "mandatoryParams" : true,
                            "parseAmount" : true
                        },
                        "customerEmail" : {
                            "kafkaKey" : "cust_mailing_add_email"
                        },
                        "customerMobile" : {
                            "kafkaKey" : "customer_add_mobile"
                        },
                        "recharge_number_2" : {
                            "kafkaKey" : "customer_add_mobile",
                            "mandatoryParams" : true
                        }
                        // "currentMinBillAmount" : {
                        //     "kafkaKey" : "emi_amount",
                        //     "parseAmount" : true
                        // },
                    }
                },
                'Fullerton India credit company limited' : {
                    product_id : *********,
                    gateway : 'bharatbillpay',
                    operator : 'Fullerton India credit company limited',
                    notificationCreateSendMinDueAmount : 1,
                    time_interval: 360,
                    mapper_table_column_with_kafka : {
                        "customerId" :  {
                            "kafkaKey" : "cust_id",
                            "mandatoryParams" : true,
                            "numeric" : true
                        },
                        "rechargeNumber" : {
                            "kafkaKey" : "loan_agreement_no",
                            "mandatoryParams" : true
                        },
                        "amount" : {
                            "kafkaKey" : "emi_amount",
                            "mandatoryParams" : true,
                            "parseAmount" : true
                        },
                        "customerEmail" : {
                            "kafkaKey" : "cust_mailing_add_email"
                        },
                        "customerMobile" : {
                            "kafkaKey" : "customer_add_mobile"
                        }
                        // "currentMinBillAmount" : {
                        //     "kafkaKey" : "loan_amount",
                        //     "parseAmount" : true
                        // }
                    }
                },
                'Fullerton India Housing Finance Limited' : {
                    product_id : 330778558,
                    gateway : 'bharatbillpay',
                    operator : 'Fullerton India Housing Finance Limited',
                    notificationCreateSendMinDueAmount : 1,
                    time_interval: 360,
                    mapper_table_column_with_kafka : {
                        "customerId" :  {
                            "kafkaKey" : "cust_id",
                            "mandatoryParams" : true,
                            "numeric" : true
                        },
                        "rechargeNumber" : {
                            "kafkaKey" : "loan_agreement_no",
                            "mandatoryParams" : true
                        },
                        "amount" : {
                            "kafkaKey" : "emi_amount",
                            "mandatoryParams" : true,
                            "parseAmount" : true
                        },
                        "customerEmail" : {
                            "kafkaKey" : "cust_mailing_add_email"
                        },
                        "customerMobile" : {
                            "kafkaKey" : "customer_add_mobile"
                        }
                        // "currentMinBillAmount" : {
                        //     "kafkaKey" : "loan_amount",
                        //     "parseAmount" : true
                        // }
                    }
                },
                'Clix' : {
                    product_id : 322580635,
                    gateway : 'bharatbillpay',
                    operator : 'Clix',
                    notificationCreateSendMinDueAmount : 1,
                    time_interval: 360,
                    mapper_table_column_with_kafka : {
                        "customerId" :  {
                            "kafkaKey" : "cust_id",
                            "mandatoryParams" : true,
                            "numeric" : true
                        },
                        "rechargeNumber" : {
                            "kafkaKey" : "loan_agreement_no",
                            "mandatoryParams" : true
                        },
                        "amount" : {
                            "kafkaKey" : "emi_amount",
                            "mandatoryParams" : true,
                            "parseAmount" : true
                        },
                        "customerEmail" : {
                            "kafkaKey" : "cust_mailing_add_email"
                        },
                        "customerMobile" : {
                            "kafkaKey" : "customer_add_mobile"
                        }
                        // "currentMinBillAmount" : {
                        //     "kafkaKey" : "loan_amount",
                        //     "parseAmount" : true
                        // }
                    }
                },
                'StashFin-CAAS' : {
                    product_id : 336587920,
                    gateway : 'bharatbillpay',
                    operator : 'StashFin',
                    notificationCreateSendMinDueAmount : 1,
                    time_interval: 360,
                    mapper_table_column_with_kafka : {
                        "customerId" :  {
                            "kafkaKey" : "cust_id",
                            "mandatoryParams" : true,
                            "numeric" : true
                        },
                        "rechargeNumber" : {
                            "kafkaKey" : "customer_add_mobile",
                            "mandatoryParams" : true
                        },
                        "amount" : {
                            "kafkaKey" : "emi_amount",
                            "mandatoryParams" : true,
                            "parseAmount" : true
                        },
                        "customerEmail" : {
                            "kafkaKey" : "cust_mailing_add_email"
                        },
                        "customerMobile" : {
                            "kafkaKey" : "customer_add_mobile"
                        },
                        "recharge_number_2" : {
                            "kafkaKey" : "DOB", /** "DD-MM-YYYY" --> "28-08-1982" */
                            "mandatoryParams" : true,
                            "parseDate" : true
                        }
                        // "currentMinBillAmount" : {
                        //     "kafkaKey" : "loan_amount",
                        //     "parseAmount" : true
                        // }
                    }
                },
                'L&T Finance Limited-CAAS' : {
                    product_id : 253861270,
                    gateway : 'lntfinance',
                    operator : 'L&T Finance Limited',
                    notificationCreateSendMinDueAmount : 1,
                    time_interval: 360,
                    mapper_table_column_with_kafka : {
                        "customerId" :  {
                            "kafkaKey" : "cust_id",
                            "mandatoryParams" : true,
                            "numeric" : true
                        },
                        "rechargeNumber" : {
                            "kafkaKey" : "loan_agreement_no",
                            "mandatoryParams" : true
                        },
                        "amount" : {
                            "kafkaKey" : "emi_amount",
                            "mandatoryParams" : true,
                            "parseAmount" : true
                        },
                        "customerEmail" : {
                            "kafkaKey" : "cust_mailing_add_email"
                        },
                        "customerMobile" : {
                            "kafkaKey" : "customer_add_mobile"
                        },
                        "recharge_number_2" : {
                            "kafkaKey" : "customer_add_mobile",
                            "mandatoryParams" : true
                        }
                        // "currentMinBillAmount" : {
                        //     "kafkaKey" : "emi_amount",
                        //     "parseAmount" : true
                        // },
                    }
                }    
            },
            staging: {
                'Hero FinCorp' : {
                    product_id : 63794219,
                    gateway : 'herofincorpapi',
                    categoryName : 'Loan Repayment',
                    operator : 'Hero FinCorp',
                    notificationCreateSendMinDueAmount : 1,
                    time_interval: 2
                },
                'Aditya Birla Finance Limited' : {
                    product_id : 1234578585,
                    gateway : 'bharatbillpay',
                    operator : 'Aditya Birla Finance Limited',
                    notificationCreateSendMinDueAmount : 1,
                    time_interval: 360,
                    mapper_table_column_with_kafka : {
                        "customerId" :  {
                            "kafkaKey" : "cust_id",
                            "mandatoryParams" : true,
                            "numeric" : true
                        },
                        "rechargeNumber" : {
                            "kafkaKey" : "loan_agreement_no",
                            "mandatoryParams" : true
                        },
                        "amount" : {
                            "kafkaKey" : "emi_amount",
                            "mandatoryParams" : true,
                            "parseAmount" : true
                        },
                        "customerEmail" : {
                            "kafkaKey" : "cust_mailing_add_email"
                        },
                        "customerMobile" : {
                            "kafkaKey" : "customer_add_mobile"
                        },
                        "recharge_number_2" : {
                            "kafkaKey" : "customer_add_mobile",
                            "mandatoryParams" : true
                        }
                        // "currentMinBillAmount" : {
                        //     "kafkaKey" : "emi_amount",
                        //     "parseAmount" : true
                        // }
                    }
                },
                'Fullerton India credit company limited' : {
                    product_id : 1234578587,
                    gateway : 'bharatbillpay',
                    operator : 'Fullerton India credit company limited',
                    notificationCreateSendMinDueAmount : 1,
                    time_interval: 360,
                    mapper_table_column_with_kafka : {
                        "customerId" :  {
                            "kafkaKey" : "cust_id",
                            "mandatoryParams" : true,
                            "numeric" : true
                        },
                        "rechargeNumber" : {
                            "kafkaKey" : "loan_agreement_no",
                            "mandatoryParams" : true
                        },
                        "amount" : {
                            "kafkaKey" : "emi_amount",
                            "mandatoryParams" : true,
                            "parseAmount" : true
                        },
                        "customerEmail" : {
                            "kafkaKey" : "cust_mailing_add_email"
                        },
                        "customerMobile" : {
                            "kafkaKey" : "customer_add_mobile"
                        }
                        // "currentMinBillAmount" : {
                        //     "kafkaKey" : "loan_amount",
                        //     "parseAmount" : true
                        // }
                    }
                },
                'Fullerton India Housing Finance Limited' : {
                    product_id : 1234578586,
                    gateway : 'bharatbillpay',
                    operator : 'Fullerton India Housing Finance Limited',
                    notificationCreateSendMinDueAmount : 1,
                    time_interval: 360,
                    mapper_table_column_with_kafka : {
                        "customerId" :  {
                            "kafkaKey" : "cust_id",
                            "mandatoryParams" : true,
                            "numeric" : true
                        },
                        "rechargeNumber" : {
                            "kafkaKey" : "loan_agreement_no",
                            "mandatoryParams" : true
                        },
                        "amount" : {
                            "kafkaKey" : "emi_amount",
                            "mandatoryParams" : true,
                            "parseAmount" : true
                        },
                        "customerEmail" : {
                            "kafkaKey" : "cust_mailing_add_email"
                        },
                        "customerMobile" : {
                            "kafkaKey" : "customer_add_mobile"
                        }
                        // "currentMinBillAmount" : {
                        //     "kafkaKey" : "loan_amount",
                        //     "parseAmount" : true
                        // }
                    }
                },
                'Clix' : {
                    product_id : 1234579144,
                    gateway : 'bharatbillpay',
                    operator : 'Clix',
                    notificationCreateSendMinDueAmount : 1,
                    time_interval: 360,
                    mapper_table_column_with_kafka : {
                        "customerId" :  {
                            "kafkaKey" : "cust_id",
                            "mandatoryParams" : true,
                            "numeric" : true
                        },
                        "rechargeNumber" : {
                            "kafkaKey" : "loan_agreement_no",
                            "mandatoryParams" : true
                        },
                        "amount" : {
                            "kafkaKey" : "emi_amount",
                            "mandatoryParams" : true,
                            "parseAmount" : true
                        },
                        "customerEmail" : {
                            "kafkaKey" : "cust_mailing_add_email"
                        },
                        "customerMobile" : {
                            "kafkaKey" : "customer_add_mobile"
                        }
                        // "currentMinBillAmount" : {
                        //     "kafkaKey" : "loan_amount",
                        //     "parseAmount" : true
                        // }
                    }
                },
                'StashFin-CAAS' : {
                    product_id : 1234580970,
                    gateway : 'bharatbillpay',
                    operator : 'StashFin',
                    notificationCreateSendMinDueAmount : 1,
                    time_interval: 360,
                    mapper_table_column_with_kafka : {
                        "customerId" :  {
                            "kafkaKey" : "cust_id",
                            "mandatoryParams" : true,
                            "numeric" : true
                        },
                        "rechargeNumber" : {
                            "kafkaKey" : "customer_add_mobile",
                            "mandatoryParams" : true
                        },
                        "amount" : {
                            "kafkaKey" : "emi_amount",
                            "mandatoryParams" : true,
                            "parseAmount" : true
                        },
                        "customerEmail" : {
                            "kafkaKey" : "cust_mailing_add_email"
                        },
                        "customerMobile" : {
                            "kafkaKey" : "customer_add_mobile"
                        },
                        "recharge_number_2" : {
                            "kafkaKey" : "DOB", /** "DD-MM-YYYY" --> "28-08-1982" */
                            "mandatoryParams" : true,
                            "parseDate" : true
                        }
                        // "currentMinBillAmount" : {
                        //     "kafkaKey" : "loan_amount",
                        //     "parseAmount" : true
                        // }
                    }
                },
                'L&T Finance Limited-CAAS' : {
                    product_id : 1200173273,
                    gateway : 'lntfinance',
                    operator : 'L&T Finance Limited',
                    notificationCreateSendMinDueAmount : 1,
                    time_interval: 360,
                    mapper_table_column_with_kafka : {
                        "customerId" :  {
                            "kafkaKey" : "cust_id",
                            "mandatoryParams" : true,
                            "numeric" : true
                        },
                        "rechargeNumber" : {
                            "kafkaKey" : "loan_agreement_no",
                            "mandatoryParams" : true
                        },
                        "amount" : {
                            "kafkaKey" : "emi_amount",
                            "mandatoryParams" : true,
                            "parseAmount" : true
                        },
                        "customerEmail" : {
                            "kafkaKey" : "cust_mailing_add_email"
                        },
                        "customerMobile" : {
                            "kafkaKey" : "customer_add_mobile"
                        },
                        "recharge_number_2" : {
                            "kafkaKey" : "customer_add_mobile",
                            "mandatoryParams" : true
                        }
                        // "currentMinBillAmount" : {
                        //     "kafkaKey" : "emi_amount",
                        //     "parseAmount" : true
                        // },
                    }
                }
            }
        },
        USER_DELETION_REASON: "userInitDeletion",
        CUSTOM_BILL_FETCH_SERVICE_ID: [1],
        PREPAID_SERVICE_ID: 4,
        DEFAULT_DUE_DATE_DIFF: 1,
        CVR_REFRESH_INTERVAL: 1800000,
        operatorsWithRegisteredUserRecents: [],
        VALIDATIONTIMEOUT: 180000,
        ARCHIVE_TABLE: 'bills_archive',
        // SUPPORTED_CREDIT_CARD_OPERATORS: {
        //     "paytmfirstcc": true,
        //     // SMS parsing CCs
        //     "airtel payments bank": true,
        //     "allahabad bank": true,
        //     "andhra bank": true,
        //     "axis bank": true,
        //     "bank of baroda": true,
        //     "bank of india": true,
        //     "bank of maharashtra": true,
        //     "canara bank": true,
        //     "central bank of india": true,
        //     "citibank": true,
        //     "corporation bank": true,
        //     "cosmos bank": true,
        //     "dena bank": true,
        //     "gopinath patil parsik janata sahakari bank": true,
        //     "hdfc bank": true,
        //     "hsbc bank": true,
        //     "icici bank": true,
        //     "idbi bank": true,
        //     "idfc bank": true,
        //     "indian bank": true,
        //     "indian overseas bank": true,
        //     "indusind bank": true,
        //     "ing vysya bank": true,
        //     "jammu and kashmir bank": true,
        //     "janata sahakari bank": true,
        //     "karnataka bank": true,
        //     "karur vysya bank": true,
        //     "kotak mahindra bank": true,
        //     "lakshmi vilas bank": true,
        //     "mastercard_airtelpaymentsbank": true,
        //     "mastercard_allahabadbank": true,
        //     "mastercard_amex": true,
        //     "mastercard_andhrabank": true,
        //     "mastercard_axisbank": true,
        //     "mastercard_bankofbaroda": true,
        //     "mastercard_bankofindia": true,
        //     "mastercard_bankofmaharashtra": true,
        //     "mastercard_canarabank": true,
        //     "mastercard_centralbankof ndia": true,
        //     "mastercard_citibank": true,
        //     "mastercard_corporationbank": true,
        //     "mastercard_cosmosbank": true,
        //     "mastercard_denabank": true,
        //     "mastercard_hdfcbank": true,
        //     "mastercard_hsbcbank": true,
        //     "mastercard_icicibank": true,
        //     "mastercard_idbibank": true,
        //     "mastercard_idfcbank": true,
        //     "mastercard_indianbank": true,
        //     "mastercard_indianoverseasbank": true,
        //     "mastercard_indusindbank": true,
        //     "mastercard_ingvysyabank": true,
        //     "mastercard_jammuandkashmirbank": true,
        //     "mastercard_janatasahakaribank": true,
        //     "mastercard_karnatakabank": true,
        //     "mastercard_karurvysyabank": true,
        //     "mastercard_kotakmahindrabank": true,
        //     "mastercard_lakshmivilasbank": true,
        //     "mastercard_orientalbankofcommerce": true,
        //     "mastercard_paytmpaymentsbank": true,
        //     "mastercard_punjabnationalbank": true,
        //     "mastercard_rblbank": true,
        //     "mastercard_royalbankofscotland": true,
        //     "mastercard_sbi": true,
        //     "mastercard_southindianbank": true,
        //     "mastercard_standardcharteredbank": true,
        //     "mastercard_statebankofhyderabad": true,
        //     "mastercard_statebankofpatiala": true,
        //     "mastercard_syndicatebank": true,
        //     "mastercard_ucobank": true,
        //     "mastercard_unionbankofindia": true,
        //     "mastercard_unitedbankofindia": true,
        //     "mastercard_vijayabank": true,
        //     "mastercard_yesbank": true,
        //     "neft_airtelpaymentsbank": true,
        //     "neft_allahabadbank": true,
        //     "neft_amex": true,
        //     "neft_andhra bank": true,
        //     "neft_andhrabank": true,
        //     "neft_axis bank": true,
        //     "neft_axisbank": true,
        //     "neft_bank of baroda": true,
        //     "neft_bank of india": true,
        //     "neft_bankofbaroda": true,
        //     "neft_bankofmaharashtra": true,
        //     "neft_canara bank": true,
        //     "neft_canarabank": true,
        //     "neft_centralbanofindia": true,
        //     "neft_citibank": true,
        //     "neft_cityunionbank": true,
        //     "neft_corporationbank": true,
        //     "neft_cosmosbank": true,
        //     "neft_dcbbanklimited": true,
        //     "neft_denabank": true,
        //     "neft_gopinathpatilparsikjanata sahakaribank": true,
        //     "neft_hdfc bank": true,
        //     "neft_hdfcbank": true,
        //     "neft_hsbc bank": true,
        //     "neft_hsbcbank": true,
        //     "neft_icici bank": true,
        //     "neft_icicibank": true,
        //     "neft_idbibank": true,
        //     "neft_idfcbank": true,
        //     "neft_indianbank": true,
        //     "neft_indianoverseasbank": true,
        //     "neft_indusindbank": true,
        //     "neft_ingvysyabank": true,
        //     "neft_jammuandkashmirbank": true,
        //     "neft_janatasahakaribank": true,
        //     "neft_karnatakabank": true,
        //     "neft_karurvysyabank": true,
        //     "neft_kotak mahindra bank": true,
        //     "neft_kotakmahindrabank": true,
        //     "neft_lakshmivilasbank": true,
        //     "neft_oriental bankofcommerce": true,
        //     "neft_paytmpaymentsbank": true,
        //     "neft_punjab national bank": true,
        //     "neft_punjabnationalbank": true,
        //     "neft_rbl bank": true,
        //     "neft_rblbank": true,
        //     "neft_royal bank of scotland": true,
        //     "neft_sb": true,
        //     "neft_sbi": true,
        //     "neft_sbmb": true,
        //     "neft_southindianbank": true,
        //     "neft_standard chartered bank": true,
        //     "neft_standardcharteredbank": true,
        //     "neft_statebankofbikaner&jaipur": true,
        //     "neft_statebankofhyderabad": true,
        //     "neft_statebankofmysore": true,
        //     "neft_statebankofpatiala": true,
        //     "neft_statebankoftravancore": true,
        //     "neft_syndicate bank": true,
        //     "neft_syndicatebank": true,
        //     "neft_ucobank": true,
        //     "neft_unionbankofindia": true,
        //     "neft_united bank of india": true,
        //     "neft_unitedbankofindia": true,
        //     "neft_vijayabank": true,
        //     "neft_yes bank": true,
        //     "neft_yesbank": true,
        //     "oriental bank of commerce": true,
        //     "paytm payments bank": true,
        //     "punjab national bank": true,
        //     "rbl bank": true,
        //     "royal bank of scotland": true,
        //     "sbi": true,
        //     "south indian bank": true,
        //     "standard chartered bank": true,
        //     "state bank of bikaner & jaipur": true,
        //     "state bank of hyderabad": true,
        //     "state bank of mysore": true,
        //     "state bank of patiala": true,
        //     "state bank of travancore": true,
        //     "syndicate bank": true,
        //     "uco bank": true,
        //     "union bank of india": true,
        //     "united bank of india": true,
        //     "vijaya bank": true,
        //     "visa_allahabad": true,
        //     "visa_andhra": true,
        //     "visa_axisbank": true,
        //     "visa_bob": true,
        //     "visa_boi": true,
        //     "visa_bom": true,
        //     "visa_canara": true,
        //     "visa_cbi": true,
        //     "visa_citibank": true,
        //     "visa_corporation": true,
        //     "visa_cosmos": true,
        //     "visa_dena": true,
        //     "visa_hdfcbank": true,
        //     "visa_hsbc": true,
        //     "visa_icicibank": true,
        //     "visa_idbi": true,
        //     "visa_idfc": true,
        //     "visa_indian": true,
        //     "visa_indusindbank": true,
        //     "visa_ing": true,
        //     "visa_iob": true,
        //     "visa_jnk": true,
        //     "visa_karnataka": true,
        //     "visa_karur": true,
        //     "visa_kotakmahindrabank": true,
        //     "visa_obc": true,
        //     "visa_pnb": true,
        //     "visa_rbl": true,
        //     "visa_rbs": true,
        //     "visa_sbi": true,
        //     "visa_sbmb": true,
        //     "visa_scb": true,
        //     "visa_southindian": true,
        //     "visa_syndicate": true,
        //     "visa_ubi": true,
        //     "visa_uco": true,
        //     "visa_vijaya": true,
        //     "visa_yes": true,
        //     "yes bank": true,
        //     "visa_fb":true,
        //     "neft_fb":true,
        //     "mastercard_fb":true,
        //     "visa_aufsb":true,
        //     "neft_aufsb":true,
        //     "sbi":true,
        //     "federal":true,
        //     "bob":true,
        //     "indus":true,
        //     "kotak":true,
        //     "au":true,
        //     "cc_federal bank":true,
        //     "cc_canara bank":true,
        //     "cc_andhra bank":true,
        //     "cc_corporation":true,
        //     "cc_hdfc bank":true,
        //     "cc_indian bank":true,
        //     "cc_indusind bank":true,
        //     "cc_kotak mahindra bank":true,
        //     "cc_ing vysya bank":true,
        //     "cc_sbi":true,
        //     "visa_karnataka":true,
        //     "cc_indian overseas bank":true,
        //     "cc_punjab national bank":true,
        //     "cc_uco bank":true,
        //     "cc_yes bank":true,
        //     "cc_standard chartered bank":true,
        //     "cc_sbm bank india":true,
        //     "cc_union bank of india":true,
        //     "cc_axis bank":true,
        //     "cc_amex":true,
        //     "cc_bank of baroda":true,
        //     "cc_bank of india":true,
        //     "cc_au small finance bank":true,
        //     "cc_citibank":true,
        //     "cc_bank of maharashtra":true,
        //     "cc_icici bank":true,
        //     "cc_industrial bank of korea":true,
        //     "cc_paytm first":true,
        //     "cc_karur vysya bank":true,
        //     "cc_saraswat bank":true,
        //     "cc_state bank of mauritius":true,
        //     "cc_united bank of india":true,
        //     "cc_hsbc bank":true,
        //     "cc_dena bank":true,
        //     "cc_cosmos":true,
        //     "cc_idfc bank":true,
        //     "cc_idbi bank":true,
        //     "cc_rbl bank":true,
        //     "cc_royal bank of scotland":true,
        //     "cc_equitas bank":true,
        //     "cc_csb bank":true,
        //     "cc_oriental bank of commerce":true,
        //     "cc_dbs bank":true,
        //     "visa_allahabad":true,
        //     "cc_vijaya bank": true,
        //     "cc_onecard indian bank":true,
        //     "cc_onecard south indian bank":true,
        //     "cc_dhanlaxmi bank":true
        // },
        PARALLEL_BILL_FETCH_HITS: {
            "bills_pspcl": 2,
            "bills_bescom": 10,
            "bills_bses": 120,
            "bills_haryanaelectricity": 10
        },
        DEFAULT_BILL_FETCH_HITS: 30,
        BILLS_COLUMN_MAPPING: {
            customer_id: "customerId",
            recharge_number: "rechargeNumber",
            product_id: "productId",
            bill_date: "billDate",
            due_date: "dueDate",
            bill_fetch_date: "billFetchDate",
            next_bill_fetch_date: "nextBillFetchDate",
            customer_mobile: "customerMobile",
            customer_email: "customerEmail",
            payment_channel: "paymentChannel",
            retry_count: "retryCount",
            created_at: "createdAt",
            updated_at: "updatedAt",
            user_data: "userData",
            payment_date: "paymentDate"
        },
        EXCLUDE_STARTUP_CONFIG: {
            DEFAULT: {
                cvr: false,
                ruleEngine: true,
                activePidLib: false,
                loadInternalCustIdLib: false,
                // mongoDb: true,
                rechargeSagaCassandraDb : true,
                dynamicConfig: false,
                rechargeConfigVault: false,
                cassandraDb:false,
                nonruCassandraDb:true,
                notificationNewCluster : false,
                serviceConfig: true,
                discontinousPlans: true
            },
            vil: {
                cvr: true,
                ruleEngine: true,
                activePidLib: false,
                loadInternalCustIdLib: true,
                mongoDb: true,
                dynamicConfig: false,
                rechargeSagaCassandraDb : false,
                notificationNewCluster : false,
                serviceConfig: true,
                discontinousPlans: true,
                nonruCassandraDb: true
            },
            retryNotify: {
                cvr: true,
                ruleEngine: true,
                activePidLib: true,
                loadInternalCustIdLib: true,
                // mongoDb: true,
                cassandraDb:false,
                nonruCassandraDb:true,
                rechargeSagaCassandraDb : true,
                notificationNewCluster : false,
                serviceConfig: true,
                discontinousPlans: true
            },
            notify: {
                cvr: true,
                ruleEngine: true,
                activePidLib: true,
                loadInternalCustIdLib: true,
                // mongoDb: true,
                cassandraDb:false,
                nonruCassandraDb:true,
                rechargeSagaCassandraDb : true,
                notificationNewCluster : false,
                serviceConfig: true,
                discontinousPlans: true
            },
            airtelPrepaidPublisher: {
                cvr: false,
                ruleEngine: false,
                activePidLib: false,
                loadInternalCustIdLib: true,
                // mongoDb: true,
                dynamicConfig: false,
                cassandraDb:true,
                nonruCassandraDb:true,
                rechargeSagaCassandraDb : true,
                notificationNewCluster : true,
                serviceConfig: true,
                discontinousPlans: true
            },
            recentBills: {
                cvr: false,
                ruleEngine: true,
                activePidLib: false,
                loadInternalCustIdLib: true,
                // mongoDb : true,
                dynamicConfig : false,
                cassandraDb:false,
                nonruCassandraDb:true,
                rechargeSagaCassandraDb : true,
                notificationNewCluster : false,
                serviceConfig: true,
                discontinousPlans: true
            },
            validationSync : {
                cvr: false,
                ruleEngine: true,
                activePidLib: false,
                loadInternalCustIdLib: true,
                // mongoDb : true,
                dynamicConfig : false,
                cassandraDb:false,
                nonruCassandraDb:true,
                rechargeSagaCassandraDb : true,
                notificationNewCluster : false,
                serviceConfig: true,
                discontinousPlans: true
            },
            reminderConsentConsumer :{
                cvr: false,
                ruleEngine: true,
                activePidLib: false,
                dynamicConfig: false,
                cassandraDb: true,
                rechargeSagaCassandraDb: true,
                notificationNewCluster: true,
                serviceConfig: true,
                discontinousPlans: true
            },
            smsParsingCCBills: {
                cvr: false,
                ruleEngine: true,
                activePidLib: false,
                loadInternalCustIdLib: false,
                // mongoDb: true,
                dynamicConfig: false,
                cassandraDb:false,
                nonruCassandraDb:true,
                rechargeSagaCassandraDb : true,
                notificationNewCluster : false,
                serviceConfig: true,
                discontinousPlans: true
            },
            emiDueConsumer: {
                cvr: false,
                ruleEngine: false,
                activePidLib: false,
                loadInternalCustIdLib: false,
                // mongoDb: true,
                dynamicConfig: false,
                cassandraDb:true,
                nonruCassandraDb:true,
                rechargeSagaCassandraDb : true,
                notificationNewCluster : true,
                serviceConfig: true,
                discontinousPlans: true
            },
            emiDueCommonConsumer: {
                cvr: false,
                ruleEngine: false,
                activePidLib: false,
                loadInternalCustIdLib: false,
                // mongoDb: true,
                dynamicConfig: false,
                cassandraDb:true,
                nonruCassandraDb:true,
                rechargeSagaCassandraDb : true,
                notificationNewCluster : true,
                serviceConfig: true,
                discontinousPlans: true
            },
            BillReminderCylinderConsumer: {                
                cvr: false,
                ruleEngine: false,
                activePidLib: true,
                loadInternalCustIdLib: true,
                // mongoDb: true,
                dynamicConfig: false,
                cassandraDb:true,
                nonruCassandraDb:true,
                rechargeSagaCassandraDb : true,
                notificationNewCluster : true,
                serviceConfig: true,
                discontinousPlans: true
            },
            removeExpiredPlanValidity: {
                cvr: true,
                ruleEngine: true,
                activePidLib: true,
                loadInternalCustIdLib: true,
                // mongoDb: true,
                cassandraDb:true,
                nonruCassandraDb:true,
                dynamicConfig: true,
                rechargeSagaCassandraDb : true, // check
                notificationNewCluster : true,
                serviceConfig: true,
                discontinousPlans: true
            },
            planValidityNotification: {
                cvr: true,
                loadInternalCustIdLib: true,
                // mongoDb: true,
                cassandraDb:false,
                nonruCassandraDb:true,
                rechargeSagaCassandraDb : true,
                notificationNewCluster : false,
                serviceConfig: true,
                discontinousPlans: true
            },
            planValidityNotificationSubscriber: {
                cvr: true,
                ruleEngine: true,
                activePidLib: false,
                loadInternalCustIdLib: true,
                // mongoDb: true,
                dynamicConfig: false,
                cassandraDb:false,
                nonruCassandraDb:true,
                rechargeSagaCassandraDb : true,
                notificationNewCluster : false,
                serviceConfig: true,
                discontinousPlans: true
            },
            billFetchResumeActivePidCron: {
                cvr: false,
                ruleEngine: true,
                activePidLib: true,
                mongoDb: true,
                dynamicConfig: false,
                discontinousPlans: true
            },
            archiveRecordsCron: {
                cvr: true,
                ruleEngine: true,
                activePidLib: true,
                // mongoDb: true,
                dynamicConfig: false,
                rechargeConfigVault: true,
                cassandraDb:true,
                nonruCassandraDb:false,
                rechargeSagaCassandraDb : true,
                notificationNewCluster : true,
                serviceConfig: true,
                discontinousPlans: true
            },
            airtelPrepaidDataLoader: {
                cvr: true,
                ruleEngine: true,
                activePidLib: true,
                loadInternalCustIdLib: true,
                // mongoDb: true,
                dynamicConfig: true,    // check
                rechargeConfigVault: true,
                cassandraDb:true,
                nonruCassandraDb:true,
                rechargeSagaCassandraDb : true,
                notificationNewCluster : true,
                serviceConfig: true,
                discontinousPlans: true
            },
            updateRecentsConsumer: {
                cvr: true,
                ruleEngine: true,
                activePidLib: true,
                loadInternalCustIdLib: true,
                // mongoDb: true,
                dynamicConfig: false,
                cassandraDb:true,
                nonruCassandraDb:true,
                rechargeSagaCassandraDb : true,
                notificationNewCluster : true,
                serviceConfig: true,
                discontinousPlans: true
            },
            updateUserConsentConsumer: {
                cvr: true,
                ruleEngine: true,
                activePidLib: true,
                loadInternalCustIdLib: true,
                // mongoDb: true,
                dynamicConfig: false,
                cassandraDb:true,
                nonruCassandraDb:true,
                rechargeSagaCassandraDb : true,
                notificationNewCluster : true,
                serviceConfig: true,
                discontinousPlans: true
            },
            syncAutomaticRecent: {
                cvr: false,
                ruleEngine: true,
                activePidLib: true,
                loadInternalCustIdLib: true,
                // mongoDb: true,
                dynamicConfig: false,
                cassandraDb:true,
                nonruCassandraDb:true,
                rechargeSagaCassandraDb : true,
                notificationNewCluster : true,
                serviceConfig: true,
                discontinousPlans: true
            },
            nonPaytmBillsConsumer: {
                cvr: false,
                ruleEngine: true,
                activePidLib: false,
                loadInternalCustIdLib: true,
                // mongoDb: true,
                dynamicConfig: false,
                cassandraDb:false,
                nonruCassandraDb: false,
                rechargeSagaCassandraDb : true,
                notificationNewCluster : false,
                serviceConfig: false,
                discontinousPlans: true
            },
            airtelBillFetchConsumer : {
                cvr: false,
                ruleEngine: true,
                activePidLib: false,
                loadInternalCustIdLib: true,
                // mongoDb: true,
                dynamicConfig: false,
                cassandraDb:false,
                nonruCassandraDb:true,
                rechargeSagaCassandraDb : true,
                notificationNewCluster : false,
                serviceConfig: true,
                discontinousPlans: true
            },
            checkActiveUsersConsumer: {
                cvr: false,
                ruleEngine: false,
                activePidLib: true,
                // mongoDb: true,
                rechargeSagaCassandraDb: true,
                dynamicConfig: false,
                rechargeConfigVault: false,
                cassandraDb: false,
                nonruCassandraDb: false,
                notificationNewCluster: true,
                discontinousPlans: true
            },
            cronUserScoreIngestionConsumer: {
                cvr: true,
                ruleEngine: true,
                activePidLib: true,
                dynamicConfig: false,
                cassandraDb: false,
                rechargeSagaCassandraDb: true,
                notificationNewCluster: true,
                discontinousPlans: true
            },
            activePaytmUsersConsumer : {
                cvr: false,
                ruleEngine: false,
                activePidLib: true,
                loadInternalCustIdLib: true,
                // mongoDb: true,
                rechargeSagaCassandraDb : true,
                dynamicConfig: false,
                rechargeConfigVault: false,
                cassandraDb:false,
                nonruCassandraDb:false,
                notificationNewCluster : true,
                serviceConfig: true,
                discontinousPlans: true
            },
            checkActiveUsersConsumer : {
                cvr: false,
                ruleEngine: false,
                activePidLib: true,
                loadInternalCustIdLib: true,
                rechargeSagaCassandraDb : true,
                dynamicConfig: false,
                rechargeConfigVault: false,
                cassandraDb:false,
                nonruCassandraDb:false,
                notificationNewCluster: true,
            },
            userScoreingestionConsumer : {
                cvr: true,
                ruleEngine: true,
                activePidLib: true,
                // mongoDb: true,
                dynamicConfig: false,
                loadInternalCustIdLib: true,
                cassandraDb:false,
                rechargeSagaCassandraDb : true,
                notificationNewCluster : true,
                serviceConfig: true,
                discontinousPlans: true
            },
            userScoreCSVIngest: {
                cvr: true,
                ruleEngine: true,
                activePidLib: true,
                // mongoDb: true,
                dynamicConfig: false,
                loadInternalCustIdLib: true,
                cassandraDb:true,
                rechargeSagaCassandraDb : true,
                notificationNewCluster : true,
                serviceConfig: true,
                discontinousPlans: true
            },
            generalSmsParser : {
                cvr: false,
                ruleEngine: false,
                activePidLib: false,
                loadInternalCustIdLib: false,
                dynamicConfig: false,
                cassandraDb:false,
                rechargeSagaCassandraDb : true,
                notificationNewCluster : false,
                serviceConfig: true,
                discontinousPlans: true
            },
            custIdRnMappingIngestion : {
                cvr: false,
                ruleEngine: true,
                activePidLib: true,
                loadInternalCustIdLib: true,
                dynamicConfig: false,
                cassandraDb: false,
                rechargeSagaCassandraDb : true,
                notificationNewCluster : true,
                serviceConfig: true,
                discontinousPlans: true
            },
            whatsappCustomerWhitelistCron: {
                cvr: true,
                ruleEngine: true,
                activePidLib: true,
                loadInternalCustIdLib: true,
                dynamicConfig: false,
                cassandraDb:false,
                rechargeSagaCassandraDb: false,
                notificationNewCluster : true,
                serviceConfig: true,
                discontinousPlans: true
            },
            cronWhatsAppWhitelistCustomerConsumer: {
                cvr: true,
                ruleEngine: true,
                activePidLib: true,
                loadInternalCustIdLib: true,
                dynamicConfig: false,
                cassandraDb:false,
                rechargeSagaCassandraDb : false,
                notificationNewCluster : true,
                serviceConfig: true,
                discontinousPlans: true
            },
            smsParsingBillPaymentDwhRealTime: {
                cvr: false,
                ruleEngine: false,
                activePidLib: false,
                // mongoDb: true,
                rechargeSagaCassandraDb: true,
                dynamicConfig: false,
                rechargeConfigVault: false,
                cassandraDb: false,
                notificationNewCluster: false,
                discontinousPlans: false
            },
            smsParsingBillPayment: {
                cvr: false,
                ruleEngine: false,
                activePidLib: false,
                // mongoDb: true,
                rechargeSagaCassandraDb: true,
                dynamicConfig: false,
                rechargeConfigVault: false,
                cassandraDb: false,
                notificationNewCluster: false,
                discontinousPlans: false,
                loadInternalCustIdLib: true
            }
        },
        MIGRATED_OPERATORS: {
            'idea': ['vodafone', 'vodafone idea', 'idea'],
            'vodafone': ['vodafone', 'vodafone idea', 'idea'],
            'vodafone idea': ['vodafone', 'vodafone idea', 'idea'],
        },
        VI_GROUP: ["vi", "vodafone", "idea", "vodafone idea"],
        // List of Configs to be fetched from Dynamic Config. Note- the same name to be inserted which is defined in the index file of config
        CONFIGS_LIST: [
            'CC_BILL_FETCH_CONFIG',
            'RECENT_BILL_CONFIG',
            'OPERATOR_TABLE_REGISTRY',
            'OPERATOR_PREPAID_TABLE_REGISTRY',
            'PREPAID_TABLE_REGISTRY',
            'OPERATOR_TEMPLATE_MAPPING',
            'OPERATOR_NOT_IN_USE_CONFIG',
            'SUBSCRIBER_CONFIG',
            'PUBLISHER_CONFIG',
            'NOTIFICATION',
            'OPERATOR_GATEWAY_REGISTRY',
            'AIRTEL_PUBLISHER_CONFIG',
            'SERVICE_TABLE_REGISTRY'
        ],
        BLACKLIST_RECENT_OPERATOR : {
            //'airteltv': 0,
            'd2h (formerly videocon d2h)' : 1,
            'indane' : 1,
            'bharatgas' : 1,
            'hp gas' : 1
        },
        D2H_GET_USER_CUSTID_COMPATIABLE_FLAG : {
            "O" : 1
            // "R" : 1
        }
    },
    production: {
        subscriptionServiceUrl: 'http://subscribeservice.mkt.paytm/v1/subscriber/sync',
        PG_CARD_INDEX_API_URI: 'https://securegw.paytm.in/savedcardservice/savedcardOpenAPIService/getCardIndexNumberByLegacyCardId',
        PG_SAVED_CARDS_BY_USER_ID_API_URI: 'https://pgp-internal-lb.paytm.in/savedcardservice/user/cards',
        SAGA_SAVED_CARDS_BY_USER_ID_API_URI: 'http://rechargesaga-internal.paytmdgt.io/api',
        //PG_SAVED_CARDS_BY_USER_ID_API_JWT_TOKEN : '', /** credentials moved to VAULT  */
        PG_SAVED_CARDS_BY_USER_ID_API_TIMEOUT : 10000,
        PG_SAVED_CARDS_BY_USER_ID_API_MAX_RECORDS_LIMIT : 10,
        PG_SAVED_CARDS_BY_USER_ID_API_PAYTMFIRSTCC_SUPPORTED_BIN_IDS : {
            438106 : 1
        },
        INTERVAL_FOR_REINITIALIZE: 15,
        USER_PREFERENCES_UPDATE_API_URL : 'https://ups-internal.paytm.com/ups/internal/v1/user-preferences',
        USER_PREFERENCES_GET_API_URL : 'https://ups-internal.paytm.com/ups/internal/v1/user-preferences',
        //USER_PREFERENCES_API_CLIENT_SECRET : '',    /** credentials will be moved to VAULT  */
        //USER_PREFERENCES_API_CLIENT_ID : '',        /** credentials will be moved to VAULT  */
        USER_PREFERENCES_WHATSAPP_NOTIFICATION_STATUS: {
            ENABLED : 1,
            DISABLED : 0,
            NOT_AVAILABLE : -1 
        },
        SAGA_SERVICE_CONFIG_URL : 'http://digitalruleengine-internal.prod.paytmdgt.io/v1/offloadcpu/data'
    },
    development: {
        subscriptionServiceUrl: 'http://localhost:9015/v1/subscriber/sync',
        PG_CARD_INDEX_API_URI: 'https://securegw-stage.paytm.in/savedcardservice/savedcardOpenAPIService/getCardIndexNumberByLegacyCardId',
        PG_SAVED_CARDS_BY_USER_ID_API_URI: 'https://run.mocky.io/v3/540b6636-a7c2-4a10-b4bf-7beb8c3a9423',
        SAGA_SAVED_CARDS_BY_USER_ID_API_URI: 'https://run.mocky.io/v3/5848a8d6-4eab-4b6f-99e8-4cb4cfd1e196',
        PG_SAVED_CARDS_BY_USER_ID_API_JWT_TOKEN : 'PG_SAVED_CARDS_BY_USER_ID_API_JWT_TOKEN',
        PG_SAVED_CARDS_BY_USER_ID_API_TIMEOUT : 10000,
        PG_SAVED_CARDS_BY_USER_ID_API_MAX_RECORDS_LIMIT : 10,
        PG_SAVED_CARDS_BY_USER_ID_API_PAYTMFIRSTCC_SUPPORTED_BIN_IDS : {
            438106 : 1
        },
        INTERVAL_FOR_REINITIALIZE: 1,
        USER_PREFERENCES_UPDATE_API_URL : 'https://ups-staging.paytm.com/ups/internal/v1/user-preferences',
        USER_PREFERENCES_GET_API_URL : 'https://ups-staging.paytm.com/ups/internal/v1/user-preferences',
        //USER_PREFERENCES_API_CLIENT_SECRET : '',
        //USER_PREFERENCES_API_CLIENT_ID : '',
        USER_PREFERENCES_WHATSAPP_NOTIFICATION_STATUS: {
            ENABLED : 1,
            DISABLED : 0,
            NOT_AVAILABLE : -1 
        },
        SAGA_SERVICE_CONFIG_URL : 'https://digitalapiproxy-staging.paytm.com/digitalrecharge/v1/offloadcpu/data'
    },
    staging: {
        PG_CARD_INDEX_API_URI: 'https://securegw-stage.paytm.in/savedcardservice/savedcardOpenAPIService/getCardIndexNumberByLegacyCardId',
        PG_SAVED_CARDS_BY_USER_ID_API_URI: 'http://inmockjava.nonprod.onus.paytmdgt.io/savedcardservice/savedcardOpenAPIService/v1/savedcardsByUserId',
        //PG_SAVED_CARDS_BY_USER_ID_API_URI: 'https://securegw-stage.paytm.in/savedcardservice/user/cards',
        //SAGA_SAVED_CARDS_BY_USER_ID_API_URI: 'http://poccassandra.nonprod.onus.paytmdgt.io/api/v2/customer',
        SAGA_SAVED_CARDS_BY_USER_ID_API_URI: 'http://inmockjava.nonprod.onus.paytmdgt.io/saga/category/staging?customer_id=',
        //PG_SAVED_CARDS_BY_USER_ID_API_JWT_TOKEN : '', /** credentials moved to VAULT  */
        PG_SAVED_CARDS_BY_USER_ID_API_TIMEOUT : 10000,
        PG_SAVED_CARDS_BY_USER_ID_API_MAX_RECORDS_LIMIT : 10,
        PG_SAVED_CARDS_BY_USER_ID_API_PAYTMFIRSTCC_SUPPORTED_BIN_IDS : {
            438106 : 1
        },
        INTERVAL_FOR_REINITIALIZE: 1,
        USER_PREFERENCES_UPDATE_API_URL : 'https://ups-staging.paytm.com/ups/internal/v1/user-preferences',
        USER_PREFERENCES_GET_API_URL : 'https://ups-staging.paytm.com/ups/internal/v1/user-preferences',
        //USER_PREFERENCES_API_CLIENT_SECRET : '', /** credentials will be moved to VAULT  */
        //USER_PREFERENCES_API_CLIENT_ID : '',     /** credentials will be moved to VAULT  */
        USER_PREFERENCES_WHATSAPP_NOTIFICATION_STATUS: {
            ENABLED : 1,
            DISABLED : 0,
            NOT_AVAILABLE : -1 
        },
        SAGA_SERVICE_CONFIG_URL : 'https://digitalapiproxy-staging.paytm.com/digitalrecharge/v1/offloadcpu/data'
    }
};
