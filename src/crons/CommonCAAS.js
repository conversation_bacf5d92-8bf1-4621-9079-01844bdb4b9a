import startup from '../lib/startup';
import MOMENT from 'moment';
import _, { reject } from "lodash";
import L from 'lgr';
import fs from 'fs'
import { parseStream } from 'fast-csv';
import { each, eachLimit, eachSeries, parallel } from 'async';
import AWSCsvIngester from '../lib/awscsvingester';
import utility from '../lib'
import OAuth from '../lib/oauth';
import ASYNC from 'async'

let serviceName = "COMMON_CAAS" 
let progressFilePath = `/var/log/digital-notification/progress-${MOMENT().format('MMYYYY')}.json`
let progressTracker = {}
import BillPush from '../lib/billPush';
/** Maintain offset and processed files in a path */
class CSVUpdate {
    constructor(options) {

        this.L = options.L;
        this.dbInstance = options.dbInstance;
        this.activePidLib = options.activePidLib;
        this.rechargeConfig = options.rechargeConfig;
        this.config = options.config;
        this.infraUtils = options.INFRAUTILS;
        this.bucketName = "digital-reminder";
        progressTracker = this.getProgressObject()
        options.isRecordProcessingCapped = _.get(options.config , ['DYNAMIC_CONFIG',serviceName,'implementDailyCapping','value'],false);
        options.cappingNumberForCurrentDay = _.get(options.config , ['DYNAMIC_CONFIG',serviceName,'dailyCappingNumber','value'],4000000);
        options.allowedCronExecutionTime = Math.floor(Math.abs(_.get(options.config , ['DYNAMIC_CONFIG',serviceName,'allowedCronExecutionTimeInMinutes','value'],null))) ? MOMENT().add(Math.floor(Math.abs(_.get(options.config , ['DYNAMIC_CONFIG',serviceName,'allowedCronExecutionTimeInMinutes','value'],null))),'minutes') : false;       // in minutes
        this.csvIngester = new AWSCsvIngester(options , this.updateProgress.bind(this))
        this.logPrefix = serviceName
        this.batchSize = _.get(options.config,['DYNAMIC_CONFIG',serviceName,'batch_size','value'],1)
        this.serviceMissing = 0
        this.operatorMissing = 0;
        this.productidMissing = 0
        this.rechargeNumberMissing = 0
        this.customerIdMissing = 0;
        this.inserted_row_count = 0;
        this.operatorMapping = {}
        this.currentFile=""
        this.OAuth = new OAuth({ batchSize: _.get(this.config, ['NOTIFICATION', 'notificationapi', 'BR_CHUNKSIZE'], 50), rechargeConfig: this.rechargeConfig });
        this.folderPath = `${_.get(options.config , ['DYNAMIC_CONFIG','COMMON_CAAS','path','value'],'digital-reminder/CAAS_Ingestion')}` 
        this.BillPush = new BillPush(options);
        this.billPushServiceRegistration = _.get(this.config, ['DYNAMIC_CONFIG', 'COMMON_CAAS', 'COMMON', 'BILL_PUSH_SERVICE_REGISTRATION'], null);
        this.billPushOperatorRegistrationAllowed = _.get(this.config, ['DYNAMIC_CONFIG', 'COMMON_CAAS', 'COMMON', 'BILL_PUSH_OPERATOR_REGISTRATION_ALLOWED'], null);
        this.BlockSmsOperatorForBillPush = _.get(this.config, ['DYNAMIC_CONFIG', 'POSTPAID_SMS_PARSING', 'COMMON', 'BILL_PUSH_BLOCK_OPERATOR_REGISTRATION'], ['uttar pradesh power corporation ltd. (uppcl)']);
        this.disabledCategoriesOperatorMap = _.get(this.config, ['DYNAMIC_CONFIG', 'COMMON_CAAS', 'COMMON', 'DISABLED_CATEGORY_OPERATOR_MAP'], {});
        this.allowedOperatorForPrepaidBillFetch = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'PREPAID_ENABLED_OPERATORS', 'OPERATORS'], null);

        this.files = []
        /** As per product requirement the CSV will have operator name without space
         * So we need to create a mapping of Operator name(with removed spaces ) -> table_name
         */
        for (const [key, value] of Object.entries(_.get(this.config, 'OPERATOR_TABLE_REGISTRY', {}))){ 
            this.operatorMapping[key.replace(/\s/g,'')]=key
            this.operatorMapping[key]=key
        }

    }

    getProgressObject(){
        let progress = {}
        progressFilePath = `/var/log/digital-notification/progress-${MOMENT().format('MMYYYY')}.json`
        this.L.info("Loading progress object from",progressFilePath)
        if(fs.existsSync(progressFilePath)){
            const progressData = fs.readFileSync(progressFilePath , 'utf-8')
            progress = JSON.parse(progressData)
        }
        this.L.info("Loaded",progress)
        return progress
    }   

    updateProgress(filename ,count  ){
        if(_.get(progressTracker , [filename] , 0 ) == -1)return;
        _.set(progressTracker , [filename] , count )
        this.L.info("Updated progess Object",JSON.stringify(progressTracker),count)
        fs.writeFileSync(progressFilePath , JSON.stringify(progressTracker , null  , 2))
    }

    filterFileByMonth(filename){
        try{
           let date = filename.split('$')[1].split('.')[0].slice(0,7)
           if(date == MOMENT().format('YYYY-MM'))return true

        }catch(err){
            return false
        }
        return false

    }

    configureKafka(done) {
        let self = this;
        ASYNC.waterfall([
            next => {
                /**
                 * Kafka publisher to publish events in NON RU Pipeline
                 */
                self.nonPaytmKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.NON_PAYTM_RECORDS.HOSTS
                });
                this.nonPaytmKafkaPublisher.initProducer('high', function (error) {
                    if (error) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:COMMON_CAAS", 'STATUS:ERROR', 'TYPE:NONRU_PUBLISHER', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                /**
                 * Kafka publisher to publish events of UMPS_SUBCRIPTION
                 */
                self.upmsPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.UPMS_PUBLISHER.HOSTS
                });
                this.upmsPublisher.initProducer('high', function (error) {
                    if (error) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:ERROR', 'TYPE:UPMS_PUBLISHER', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            }
        ], function (error) {
            if (error) {
                self.L.critical('configureKafka', 'Could not initialize Kafka', error);
                return done(error)
            }
            return done(null);
        });
    }

    start(callback){

        let self = this;
        progressTracker = this.getProgressObject()

        self.configureKafka(function (error) {
            if (error) {
                self.L.critical('CommonCAAS :: start', 'unable to configure kafka', error);
                process.exit(0);
            }
            else {
                self.L.log('CommonCAAS :: start', 'Kafka Configured successfully !!');
            }
        });

        try{
            this.csvIngester.configure(this.bucketName, this.logPrefix,this.batchSize)
        }catch(error){
            self.L.critical(this.logPrefix , "Cannot initialize AWS")
            return callback(error)
        }

        self.L.info("Getting Files in the folder")


        this.csvIngester.getFileNames(this.folderPath,function(err , data){
            if(err){
                self.L.error("Error while getting files")
                return callback(err)
            }else{
                data = _.filter(data , self.filterFileByMonth)
                return eachSeries(data , self.processEachFile.bind(self) , callback)
            }
        })
      
    }

    processEachFile(filename , callback){
        if(_.get(progressTracker , filename, null) == -1){
            /** File has already been processed we can skip*/
            this.L.info("Skipping file ",filename , "as it has been already processed")
            return callback()
        }
        this.L.info("Processing file :- ",filename)
        this.currentFile = filename
        let skipRows = _.get(progressTracker , [filename], 0) 
        this.csvIngester.start(this.processRecordinBatch.bind(this),filename , function(error , data){
            return callback()
        },skipRows)
    }

    async processRecordinBatch(data){
        let self = this;
        return new Promise((resolve , reject)=>{
            eachLimit(data ,self.batchSize, function(record , cb){
                self.processRecord(function(){
                    cb()
                } ,record)
            },function(error){
                if(error){
                    self.L.error(self.logPrefix , "Error while processing batch",error)
                    return reject(error)
                }
                return resolve()
            })
        })
    }
    
    async processRecord(cb, data) {
        let self=this;

        let service = _.get(data, 'service', null);
        let operator = _.get(data, 'operator', null);

        if(operator!=null){
            operator = _.get(self.operatorMapping,  operator, null);  
        }

        if (self.isServiceOperatorDisabled(service, operator)) {
            self.L.verbose(`processRecord :: Skipping record as service ${service} and operator ${operator} is disabled`);
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:COMMON_CAAS', 'STATUS:RECORD_SKIPPED', `SERVICE:${service}`, `OPERATOR:${operator}`, 'TYPE:DISABLED_CATEGORY_OPERATOR']);
            return cb();
        }

        if(_.get(data , 'customer_id',null) == null  && _.get(data , 'mobileNumber',null)!=null && _.get(data , 'OauthTried',0)==0){
            _.set(data , 'OauthTried',1)
            return self.getCustDetails(data).then(cid=>{
                    _.set(data , 'customer_id',cid)
                    return self.processRecord(cb , data)
            })
        }
        let nbfd = _.get(data , 'NBFD',null)
        if(nbfd !=null){
            let date;
            date = MOMENT(nbfd , 'YYYY-MM-DD')
            if(date.isValid() == false) date = MOMENT(nbfd , 'DD-MMM-YY')
            if(date.isValid() === true){
                nbfd = date.format("YYYY-MM-DD HH:mm:ss")
            }
            else nbfd = null

        }
        if(nbfd == null){
            /** if NBFD is not available make it d+1 */
            nbfd = MOMENT().add(1,'d').startOf('day').format("YYYY-MM-DD HH:mm:ss")
        }
        let userData = _.pick(data, [
            'recharge_number_2',
            'recharge_number_3',
            'recharge_number_4',
            'recharge_number_5',
            'recharge_number_6',
            'recharge_number_7',
            'recharge_number_8',
        ])
        let params={
            'service': _.get(data, 'service', null),
            'operator': operator ,
            'productid': _.get(data, 'product_id', null),
            'rechargeNumber': _.get(data, 'recharge_number',  _.get(data , 'rechargeNumber' , null)),
            'customer_id':_.get(data , 'customer_id',null),
            'status':'0',
            'service_id':'0',
            'is_automatic':'0',
            'next_bill_fetch_date':nbfd,
            'paytype' : _.get(data , 'paytype',null),
            'userData' : _.pickBy(userData, function(param){ return ! _.isEmpty(param) })
        }
        let tableName = _.get(self.config,  ['OPERATOR_TABLE_REGISTRY',operator], null);  
        if(!params.customer_id){           
            self.L.error(self.currentFile , 'Customer ID missing',params)
            self.customerIdMissing++
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:COMMON_CAAS', 'STATUS:PARAMS_MISSING', `SERVICE:${params.service}`,`OPERATOR:${params.operator}`,'TYPE:CUSTOMER_ID_NOT_FOUND_FROM_CSV']);
            return cb();
        }
        if(!params.next_bill_fetch_date){
            self.L.error(self.currentFile , 'Next bill fetch date Missing',params)
       
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:COMMON_CAAS', 'STATUS:PARAMS_MISSING', `SERVICE:${params.service}`,`OPERATOR:${params.operator}`,'TYPE:NBFD_NOT_FOUND_FROM_CSV']);
            return cb();
        }
        if(!params.rechargeNumber){
            self.L.error(self.currentFile , 'Recharge number missing',params)
            self.rechargeNumberMissing++;
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:COMMON_CAAS', 'STATUS:PARAMS_MISSING', `SERVICE:${params.service}`,`OPERATOR:${params.operator}`,'TYPE:RECHARGE_NUMBER_NOT_FOUND_FROM_CSV']);
            return cb();
        }
        if(!params.operator){
            self.L.error(self.currentFile , 'Opertator missing',params  , _.get(data, 'operator', null))
            self.operatorMissing++;
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:COMMON_CAAS', 'STATUS:PARAMS_MISSING', `SERVICE:${params.service}`,`OPERATOR:${params.operator}`,'TYPE:OPERATOR_NOT_FOUND_FROM_CSV']);
            return cb();
        }
        if(!params.service){
            self.L.error(self.currentFile , 'Service name missing',params)
            self.serviceMissing++;
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:COMMON_CAAS', 'STATUS:PARAMS_MISSING', `SERVICE:${params.service}`,`OPERATOR:${params.operator}`,'TYPE:SERVICE_NOT_FOUND_FROM_CSV']);
            return cb();
        } 
        if(!params.productid){
            self.L.error(self.currentFile , 'Product Id missing',params)
            self.productidMissing++;
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:COMMON_CAAS', 'STATUS:PARAMS_MISSING', `SERVICE:${params.service}`,`OPERATOR:${params.operator}`,'TYPE:PRODUCT_ID_NOT_FOUND_FROM_CSV']);
            return cb();
            
        } 
        if(!params.paytype){
            self.L.error(self.currentFile , 'Paytype missing',params)
            self.productidMissing++;
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:COMMON_CAAS', 'STATUS:PARAMS_MISSING', `SERVICE:${params.service}`,`OPERATOR:${params.operator}`,'TYPE:PAYTYPE_NOT_FOUND_FROM_CSV']);
            return cb();
        } 

        //push data for billPush Registration
        if ((self.billPushServiceRegistration && self.billPushServiceRegistration.includes(_.get(params, 'service', null))) || (self.billPushOperatorRegistrationAllowed && self.billPushOperatorRegistrationAllowed.includes(_.get(params, 'operator', null)))) {
            self.BillPush.pushToRegistrationProcess(self, params, params, 'commonCAAS');
        }

        if(!tableName){
            self.L.error(self.currentFile , 'Table name not found for operator : ',params.operator)
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:COMMON_CAAS', 'STATUS:PARAMS_MISSING',`SERVICE:${params.service}`,`OPERATOR:${params.operator}`, 'TYPE:TABLE_NOT FOUND']);
            return cb();    
        }
        if (self.allowedOperatorForPrepaidBillFetch.includes(operator)) {
            self.L.info(self.currentFile, `Table name ${tableName} is a prepaid table. Checking if data already exists.`);
            let prepaidTableName = tableName + '_prepaid';
            
            let isPrepaidRecordExist = false ;
            try {
            isPrepaidRecordExist = await self.checkInPrepaid(prepaidTableName, params);
            if(isPrepaidRecordExist){
                return cb ();
            }
        } catch(e){
            return cb (e);
        }
        }

            ASYNC.waterfall([
                next => {
                    self.insertRecords(tableName, params, (error) => {
                        if (error) {
                            utility._sendMetricsToDD(1, [
                                'REQUEST_TYPE:COMMON_CAAS',
                                'STATUS:RECORDS_NOT_INSERTED',
                                `SERVICE:${params.service}`,
                                `OPERATOR:${params.operator}`,
                                'TYPE:RECORDS_NOT_INSERTED_INTO_TABLE'
                            ]);
                            self.L.error('error', error);
                            return next(error);
                        }
        
                        self.L.info(self.currentFile, 'Record inserted');
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:COMMON_CAAS',
                            'STATUS:RECORDS_INSERTED',
                            `SERVICE:${params.service}`,
                            `OPERATOR:${params.operator}`,
                            'TYPE:RECORDS_INSERTED_INTO_TABLE'
                        ]);
                        self.L.log(`insertRecords :: Inserted into ${tableName} for customerId:${params.customer_id},Processed records so far:${++self.inserted_row_count}`);
                        return next();
                    });
                },
                next => {
                    let payload = self.getNonPaytmDeletePayload(params);
                    self.pushRecordToNONPAYTM(next, payload);
                }
            ], (error) => {
                return cb(error);
            });
    }

   
    insertRecords(table_name,params,cb){
       
       let self = this;
        
       let extra = {
        billSource : 'COMMON_CAAS'
       } 
       const query = `INSERT INTO ${table_name} \
       (customer_id, recharge_number, product_id, operator, next_bill_fetch_date, service , extra , paytype, user_data) \
       values (?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE next_bill_fetch_date=VALUES(next_bill_fetch_date),extra=JSON_SET(COALESCE(extra,'{}' ) ,'$.billSource','COMMON_CAAS')`; 

       const param = [
           params.customer_id,
           params.rechargeNumber,
           params.productid,
           params.operator,
           params.next_bill_fetch_date,
           params.service,
           JSON.stringify(extra),
           params.paytype,
           JSON.stringify(params.userData)
       ];

       self.dbInstance.exec((error, res) => {
           if (error) {
               self.L.critical('writeCustomerDetails::', query, error);
           }
           return cb(error,res);
       }, 'DIGITAL_REMINDER_MASTER', query, param);
}

    async getCustDetails(record){
        let self=this;
        try {
            let mobileNumber = _.get(record, 'mobileNumber', '');
            if(mobileNumber == '')return null

                let queryString = `?fetch_strategy=USERID,BASIC&phone=${mobileNumber}`;

                self.L.log(`getCustDetails:: from OAuth.fetchCustomerDetail API for _mobileNumber:${queryString}`);
                let response =  await self.OAuth.fetchCustomerDetail(queryString); 

                if (_.get(response, ['userId'], '') === '') {
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:ELECTRICITY_CAAS', 'STATUS:CUSTOMER_DETAILS_NOT_FOUND', 'TYPE:CUST_ID_NOT_FOUND_FROM_OATH_API']);
                    self.L.log(`getCustDetails:: customer details not found from OAuth API for _mobileNumber:${mobileNumber}`);
                    return null;
                }
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:ELECTRICITY_CAAS', 'STATUS:CUSTOMER_DETAILS_FOUND', 'TYPE:CUST_DETAILS_FOUND_FROM_OATH_API']);
                self.L.log(`getCustDetails:: customer details found from OAuth API for _mobileNumber:${mobileNumber}_customerId:${_.get(response, ['userId'], '')}`);

                return  _.get(response, ['userId'], null)
        }
        catch (error) {
            return null;
        }

    }

    getNonPaytmDeletePayload(params){
        let nonPaytmKafkaPayload = {
            customerId: params.customer_id,
            service: params.service,
            paytype: params.paytype,
            productId: params.productid,
            operator: params.operator,
            rechargeNumber: params.rechargeNumber,
            dbEvent: 'delete'
        }
        return nonPaytmKafkaPayload;
    }

    pushRecordToNONPAYTM(done, payload) {
        let self = this;

        self.nonPaytmKafkaPublisher.publishData([{
            topic: _.get(self.config.KAFKA, 'SERVICES.NON_PAYTM_RECORDS.TOPIC', ''),
            messages: JSON.stringify(payload)
        }], (error) => {
            if (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:COMMON_CAAS",'STATUS:ERROR',"TYPE:NON_PAYTM_RECORDS"]);
                self.L.critical('COMMON_CAAS :: nonPaytmKafkaPublisher', 'Error while publishing message in Kafka on topic NON_PAYTM_RECORDS - MSG:- ' + JSON.stringify(payload), error);
                return done(error);
            } else {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:COMMON_CAAS",'STATUS:SUCCESS',"TYPE:NON_PAYTM_RECORDS"]);
                self.L.log('COMMON_CAAS :: nonPaytmKafkaPublisher', 'Message published successfully in Kafka', ' on topic NON_PAYTM_RECORDS', JSON.stringify(payload));
                return done(null);
            }
        })
    }

    isServiceOperatorDisabled(service, operator) {
        service = service?.toLowerCase();
        operator = operator?.toLowerCase();
        return this.disabledCategoriesOperatorMap[service] && this.disabledCategoriesOperatorMap[service].includes(operator);
    }

    checkIfRecordExists(tableName, params, callback) {
        const self = this;
        const query = `SELECT * FROM ${tableName} WHERE customer_id = ? AND operator = ? AND service = ? AND recharge_number = ?`;
        const values = [
            params.customer_id,
            params.operator,
            params.service,
            params.rechargeNumber
        ];
    
        self.dbInstance.exec(
            (err, result) => {
                if (err) {
                    self.L.error(self.currentFile, `Error executing query to check record existence in ${tableName}:`, err);
                    return callback(null, false);
                }
    
                const exists = Array.isArray(result) && result.length > 0;
    
                if (exists) {
                    self.L.info(self.currentFile, `Record exists in ${tableName} for customerId: ${params.customer_id}`);
                } else {
                    self.L.info(self.currentFile, `No record found in ${tableName} for customerId: ${params.customer_id}`);
                }
    
                return callback(null, exists);
            },
            'DIGITAL_REMINDER_SLAVE', // pattern string for cluster
            query,
            values
        );
    }
    async checkInPrepaid(prepaidTableName, params){
        const self = this;
        return new Promise((resolve, reject) => {  
        self.checkIfRecordExists(prepaidTableName, params, (err, exists) => {
            if (err) {
                self.L.error(self.currentFile, 'Error checking if record exists: ', err);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:COMMON_CAAS', 'STATUS:RECORDS_NOT_FETCHED', `SERVICE:${params.service}`,`OPERATOR:${params.operator}`,'TYPE:RECORDS_NOT_FETCHED_TABLE']);

                reject(err);
            }
            if (exists) {
                self.L.info(self.currentFile, `Record already exists in ${prepaidTableName} for customerId: ${params.customer_id}. Skipping insertion.`);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:COMMON_CAAS', 'STATUS:RECORD_ALREADY_EXISTS', `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, 'TYPE:PREPAID_RECORD_EXISTS']);
                resolve(true);
            } 
            else {
                resolve(false)
            }
            
            // Proceed with insertion if record does not exist
        });
        }
    )
}
}



(function main() {
    if (require.main === module) {
        startup.init({
              
        }, function (err, options) {
            let script;
            try{
            script = new CSVUpdate(options);
            script.start(
                function (err) {
                    setTimeout(function () {
                        if (err) {
                            console.log("main::Error" + err);
                            process.exit(1);
                        } else {
                            console.log("main::completed");
                            process.exit(0);
                        }
                    }, 1000);
                })
            }catch(err){
                options.L.error(err)
                process.exit(1)
            }
          
        });
    }
})();


export default CSVUpdate