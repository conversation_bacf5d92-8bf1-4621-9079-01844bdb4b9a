/*jshint esversion: 8 */
/* NODE internal */
import HTTP from 'http'
import HTTPS from 'https'

/* NPM Third Party */
import EXPRESS from 'express'
import PROGRAM, { option } from 'commander'
import _ from 'lodash'
import BODYPARSER from 'body-parser'
import Q from 'q'

/* NPM Paytm*/
import L from 'lgr'
import INFRAUTILS from 'infra-utils'

/* Project Files */
import PROC from './lib/ptm_proc'
import CONTROLLERS from './controllers'
import ROUTER from './routes'
import SERVICES from './services'
import CRONS from './crons'
import LIB from './lib'
import OS from 'os'
import { scheduleJob } from 'node-schedule'
import CSVUpdate from './crons/CommonCAAS'
import CustomNotifications from './crons/customNotifications'
import HeuristicCustomNotifications from './crons/heuristicCustomNotifications'
import CCIngestion from './crons/CC_Ingestion'
import CCIngestionKafka from './crons/CC_Ingestion_Kafka'
import AirtelKafkaPublisher from './services/airtelKafkaPublisher'
import AIRTELCSVINGEST from './crons/airtel_prepaid_csv_loader'
import USERSCORECSVINGEST from './crons/user_score_csv_ingestor'
import WHATSAPPCUTOMERWHITELIST from './crons/whatsapp_customer_whitelist_csv'
import CustIdRnMappingIngestion from './crons/custid_rn_mapping_ingestion'
import MOMENT from 'moment'
import utility from './lib'
import Logger from './lib/logger'
// import S3CustomerUpdatesProcessor from './crons/s3_customer_updates_processor' // File not found - commented out


// import profiler from 'v8-profiler-node8'; // Module not found - commented out
// import fs from 'fs' // Not used - commented out

/* Global Variables */
let PORT = _.get(process, 'env.PORT', 7000)  //default port 7000


let options,
    deferred = Q.defer();

/*
    Command Line arguments
*/
let logger;

PROGRAM
    .option('-v, --verbose', 'Run in verbose mode')
    .option('-s, --subscriberMode', 'Run as Subscriber')
    .option('-r, --notify [item(s)]', 'Run as notification service', function (v, m) { m.push(v); return m; }, [])
    .option('-z, --retryNotify [item(s)]', 'Run as retry notification service', function (v, m) { m.push(v); return m; }, [])
    .option('-t, --statusReport', 'Status report cron')
    .option('-d, --notificationStatus', 'notification status service')
    .option('--customNotificationCreate', 'bill reminder notification cron')
    .option('--heuristicCustomNotificationCreate', 'bill reminder notification cron')
    .option('-h, --billReminderNotification', 'bill reminder notification cron')
    .option('--billReminderNotificationRealtime', 'bill reminder notification cron for realtime')
    .option('--billReminderNotificationNonRuRealtime', 'bill reminder notification cron for non ru realtime')
    .option('--billReminderNotificationNonRu', 'bill reminder notification cron for non ru')
    .option('-b, --batch [batch(s)]', 'Run Multiple operators with one publisher', function (v, m) { m.push(v); return m; }, [])
    .option('-p, --planValidityNotification', 'plan validity notification cron')
    .option('-m, --rechargeNudgeRechargeConsumer', 'Recharge nudge consumer for processing recharge data')
    .option('-c, --rechargeNudgeValidationConsumer [mode]', 'Recharge nudge consumer for processing validation data and sending notification. Mode can be d or d+2')
    .option('--rechargeNudgeServiceRechargeConsumer', 'Recharge nudge consumer for processing recharge data')
    .option('--rechargeNudgeServiceValidationConsumer', 'Validation nudge consumer for processing recharge data')
    .option('-i, --recentBills', 'Service to sync bills data')
    .option('-j, --notificationService', 'Service to manage notification')
    .option('-o, --notificationReport', 'sends notification report daily')
    .option('-f, --syncReminder', 'sync reminder with automatic flag')
    .option('-k, --billDuePublisher', 'publish due bills in kafka')
    .option('--billDuePublisherBatch1', 'publish due bills in kafka')
    .option('--billDuePublisherBatch2', 'publish due bills in kafka')
    .option('--billDuePublisherBatch3', 'publish due bills in kafka')
    .option('--billDuePublisherBatch4', 'publish due bills in kafka')
    .option('--billDuePublisherEndOfDay', 'publish due bills in kafka')
    .option('--billDuePublisherPaytmPostpaid', 'publish due bills for PP in kafka')
    .option('-a, --airtelPrepaidPublisher', 'Airtel prepaid publisher service')
    .option('--validationSync', 'Validation Sync service')
    .option('-q, --smsParsingCCBills', 'SMS parsing cc Kafka consumer')
    .option('--smsParsingCCBillsRealtime', 'SMS parsing cc realtime Kafka consumer')
    .option('--smsParsingCCBillsDwhRealtime', 'SMS parsing cc dwh realtime Kafka consumer')
    .option('--smsParsingFasTag', 'SMS parsing FasTag Kafka consumer')
    .option('-g, --planValidityNotificationSubscriber [item(s)]', 'Plan Validity Notification subscriber service', function (v, m) { m.push(v); return m; }, [])
    .option('-x, --removeExpiredPlanValidity', 'remove Expired Plan validity cron')
    .option('-y, --storeHistoricalRecords', 'Storing historical records')
    .option('--reminderScheduler', 'publish bill gen bills in kafka')
    .option('--oldBillDuePublisher', 'publish old bills in kafka based on old_bill_fetch_date')
    .option('--prepaidHistoricRecords', 'store Plan validity history')
    .option('--planValidityRMQConsumer', 'Plan Validity RMQ Consumer')
    .option('--allTransactionsConsumer', ' All Transactions Consumer')
    .option('--emiDueConsumer', 'EMI due Kafka consumer')
    .option('--updateRecentsConsumer', 'update partial payments of recents')
    .option('--emiDueCommonConsumer', 'EMI due Common Kafka consumer')
    .option('--billReminderCylinderConsumer', 'Cylinder Kafka consumer')
    .option('--operatorUpNotification', 'Notifaction to dropped cust when operator is up')
    .option('--updateUserConsentConsumer', 'Update user consents consumer')
    .option('--billReminderCylinderConsumer', 'Cylinder Kafka consumer')
    .option('--syncAutomaticRecent', 'sync Automatic status and date with Recent')
    .option('--nonPaytmBillsConsumer', 'consume non paytm data and update on cassandra DB')
    .option('--smsParsingBillPayment', 'SMS parsing paid bill kafka consumer')
    .option('--smsParsingBillPaymentDwhRealTime', 'SMS parsing paid bill kafka consumer')
    .option('--ccsmsParsingBillPayment', 'SMS parsing paid bill kafka consumer')
    .option('--electricitysmsParsingBillPayment', 'SMS parsing paid bill kafka consumer')
    .option('--electricitysmsParsingBillPaymentDwhRealtime', 'SMS parsing paid bill kafka consumer')
    .option('--dthsmsParsingBills', 'dth SMS parsing bill kafka consumer')
    .option('--rentsmsParsingBillPayment', 'Rent SMS parsing paid bill kafka consumer')
    .option('--smsParsingLoanEmi', 'SMS parsing Loan emi bill kafka consumer')
    .option('--paytmPostpaid', 'paytm postpaid bills kafka consumer')
    .option('--realtimeSmsParsingPrepaid', 'Realtime sms parsing for prepaid category')
    .option('--realtimeSmsParsingPostpaid', 'Realtime sms parsing for postpaid category')
    .option('--notificationFallbackConsumer', 'notification fallback consumer')
    .option('--csvCron', 'Crons for CSV')
    .option('--fallbackCustomerIdIngester', 'Cron to fetch CSV from S3, and ingest to Cassandra DB')
    .option('--airtelKafkaPublisher', '')
    .option('--airtelPrepaidIngest', 'Cron to insert/delete airtel user base')
    .option('--airtelBillFetchConsumer', 'Airtel Bill Fetch Consumer')
    .option('--airtelPrepaidBllDuePublisher', 'airtelPrepaidBllDuePublisher for due date')
    .option('--ccPublisher [ccPublisher(s)]', "Publisher for Credit card")
    .option('--ccIngestion', "CC ingester for Credit card")
    .option('--ccIngestionKafka', "CC ingester Kafka for Credit card")
    .option('--pgTokenDeletion', 'PG Token Deletion')
    .option('--oldBillPushToSaga', "Paytm Postpaid old bill push to saga")
    .option('--customNotificationCron', 'Crons for custom notification CSV')
    .option('--ccIngestionNonPaytm', 'CC ingester for non paytm Credit card')
    .option('--notifyRejectedBills', "Send notification to users whose bills have been rejected")
    .option('--heuristicCustomNotificationCron', 'Crons for custom notification CSV')
    .option('--archivalRecords', 'Archive Records from main table to archive table')
    .option('--activePaytmUsersConsumer', "Store customer_id and payment date of success recharges")
    .option('--checkActiveUsersConsumer', "Check if a user is active by querying active users table")
    .option('--genericSmsParsingRealTime [item(s)]', 'Run as realtime general parser')
    .option('--userScoreingestionConsumer', "updates  nbfd of bills_x table records as per priority basis user score")
    .option('--userScoreCSVIngest', 'Cron to ingest user score')
    .option('--whatsappCustomerWhitelistCron', 'Cron to ingest whatsapp customer whitelist')
    .option('--whatsappCustomerWhitelistConsumer', 'whatsapp customer whitelist consumer')
    .option('--generalSmsParser [kafkaTopic(s)]', "consumes payloads from dwh smsParsing pipeline and after remodeling directly publishes to CT kafka")
    .option('--custIdRnMappingIngestion', "csv ingestion to store customer id recharge number mapping")
    .option('--billDuePrepaidPublisher', 'publish prepaid bills in kafka')
    .option('--cronUserScoreIngestion', "consumes user scores csv payload data and inserts in cassandra db")
    .option('--reminderConsentConsumer', 'Reminder Consent Consumer')
    .option('--expiredCAPublisher', "Publish in nonru pipeline for expired users")
    .option('--s3CustomerUpdatesProcessor', 'Cron to process customer updates from S3 and publish to Kafka')
    .option('--customerLastActivityUpdateConsumer', 'Cron to process customer updates from S3 and publish to Kafka')
    .option('--whatsappNotificationFallbackConsumer', 'whatsapp notification fallback consumer')
    .option('--smsParsingValidator', 'validate sms parser payload and pushing mismatches metrics')
    .parse(process.argv);

/*
    setting log level as verbose
*/
if (PROGRAM.verbose) {
    L.setLevel('verbose');
}

Q(undefined)
    // .then(function(){
    //     const env = process.env.NODE_ENV || "development";
    //     const hostName = OS.hostname().split("-");

    //     process.env['PINPOINT_APPLICATION_NAME'] = `ru-${hostName[0].substring(0, 16)}-${env[0]}`;
    //     process.env['PINPOINT_AGENT_ID'] = hostName.splice(hostName.length - 2).join("-");
    //     process.env['PINPOINT_COLLECTOR_IP'] = "pinpoint-central-collector.mypaytm.com";
    //     require('pinpoint-node-agent');
    //     return Q(undefined);
    // })
    .then(function () {
        // calling startup lib
        let def = Q.defer();
        LIB.init(PROGRAM, function (error, optionsResponse) {
            L.log('app', 'startup lib initialisation complete, error if any is:', error);

            if (error) {
                def.reject(error);
            } else {
                options = optionsResponse;
                logger = new Logger(options);

                let parentService = process.argv
                    .slice(2)
                    .find(arg => arg.startsWith('--')) // Only match double-dash arguments
                    ?.replace(/^--/, '');

                L.info('Logger initialized for service:', parentService);

                // logger.overrideLoggerMethods(parentService);
                // options.mongoDbInstance = new INFRAUTILS.mongo(options.config.MONGO.MASTER);
                def.resolve();
            }
        });

        return def.promise;
    })
    .then(function () {

        if (PROGRAM.subscriberMode) {
            L.info('Subscriber mode enabled ... ')
            let serviceInstance = null;
            try {
                // options.mongoDbInstance.connect(function (err, res) {
                //     if (err) {
                //         L.critical('Unable to connect to Mongo DB for Subscriber Mode... ', err);
                //     } else {
                //         L.log('Connected to Mongo DB successfully for Subscriber Mode... ');
                //     }
                serviceInstance = new SERVICES.billSubscriber(options);
                serviceInstance.start();
                // });
            } catch (ex) {
                L.critical("Exception occured in Subscriber Mode... ", ex);
                serviceInstance = new SERVICES.billSubscriber(options);
                serviceInstance.start();
            }

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
            });

        }
        else if (PROGRAM.airtelPrepaidPublisher) {
            L.info('Airtel Publisher mode enabled ... ')

            let publisher = new SERVICES.airtelPublisher(options);
            publisher.start();

            process.addListener('SIGUSR1', function () {
                publisher.suspendOperations();
            });
        }
        else if (PROGRAM.validationSync) {
            L.info('Validation Sync mode enabled ... ');

            let validationsync = new SERVICES.ValidationSync(options);
            validationsync.start();

            process.addListener('SIGUSR1', function () {
                validationsync.suspendOperations();
                L.log("app :: Application terminated Gracefully");

            });

        }
        else if (PROGRAM.batch && PROGRAM.batch.length > 0) {
            L.info('Publisher mode enabled ... ')

            try {
                // options.mongoDbInstance.connect(function (err) {
                //     if (err) {
                //         L.critical('Unable to connect to Mongo DB for Publisher Mode... ', err);
                //     } else {
                //         L.log('Connected to Mongo DB successfully for Publisher Mode... ');

                let batchId = PROGRAM.batch[0];

                options.batchId = batchId;

                let publisherManager = new SERVICES.publisherManager(options);
                let operators = _.get(options.config, ['PUBLISHER_CONFIG', 'PUBLISHER', batchId], []);
                L.log('Running for operators: ', operators)
                let difference = _.get(options.config, 'DYNAMIC_CONFIG.PUBLISHER_CONFIG.PUBLISHER_START_DELAY', 20 * 1000);
                let i = 1;
                operators.forEach(operator => {
                    let tableName = _.get(options.config, ['OPERATOR_TABLE_REGISTRY', operator], null)
                    if (tableName && publisherManager.createPublisherForOperator(tableName, operator)) {
                        L.info("Initalization for ", operator, "will start after", (i * difference) / 1000, " seconds")
                        setTimeout(() => {
                            L.info("Initialization started for operator:", operator)
                            publisherManager.startPublisherForOperator(operator)

                        }, i * difference);
                        i++;
                    }
                    else {
                        L.error('No table configured for the operator: ', operator);
                    }
                    let prepaidOperatorsAllowed = _.get(options.config, 'DYNAMIC_CONFIG.PREPAID_BILL_FETCH_CONFIG.PREPAID_ENABLED_OPERATORS.OPERATORS', null);
                    if (prepaidOperatorsAllowed && prepaidOperatorsAllowed.includes(operator)) {
                        let prepaidTableName = tableName + '_prepaid';
                        if (prepaidTableName && publisherManager.createPublisherForOperator(prepaidTableName, operator, false, true)) {
                            L.info("Initalization for prepaid ", operator, "will start after", (i * difference) / 1000, " seconds")
                            setTimeout(() => {
                                L.info("Initialization started for prepaidOperator:", operator)
                                publisherManager.startPublisherForOperator(operator, true)

                            }, i * difference);
                            i++;
                        }
                        else {
                            L.error('No Prepaid table configured for the operator: ', operator);
                        }
                    }
                })
                process.addListener('SIGUSR1', function () {
                    publisherManager.stopAllPublishers();
                    L.log("app :: Application terminated Gracefully");
                });
                //     }
                // });
            } catch (ex) {
                L.critical("Exception occured in Publisher Mode... ", ex);
            }
        }
        else if (PROGRAM.notify && PROGRAM.notify.length > 0) {
            L.info('Notification mode enabled ... ')
            let params = PROGRAM.notify[0].split(',');
            options.categoryId = parseInt(params[0]);

            let tps = params.length > 1 && params[1] && parseInt(params[1]);
            if (!isNaN(tps) && tps > 0) options.tps = tps;
            let serviceInstance = new SERVICES.notify(options);
            serviceInstance.start();

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");

            });
        }
        else if (PROGRAM.genericSmsParsing && PROGRAM.genericSmsParsing.length > 0) {
            L.info('GenericSmsParsing mode enabled ... ')
            let params = PROGRAM.genericSmsParsing[0].split(',');
            options.categoryId = params[0];
            options.saveForAnalyticsInCassandraAndKafka = true;

            let serviceInstance = new SERVICES.GenericSmsParsing(options);
            serviceInstance.start();

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");

            });
        }
        else if (PROGRAM.genericSmsParsingRealTime && PROGRAM.genericSmsParsingRealTime.length > 0) {
            L.info('GenericSmsParsing Real Time mode enabled ... ')
            let params = PROGRAM.genericSmsParsingRealTime[0].split(',');
            options.categoryId = params[0];
            options.genericDwhRealTimeSmsParsing = true;
            options.saveForAnalyticsInCassandraAndKafka = true;

            let serviceInstance = new SERVICES.GenericSmsParsing(options);
            serviceInstance.start();

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");

            });
        }
        else if (PROGRAM.retryNotify && PROGRAM.retryNotify.length > 0) {
            L.info('Retry Notification mode enabled ... ')
            options.tps = parseInt(PROGRAM.retryNotify[0]);
            let serviceInstance = new SERVICES.notify(options);
            serviceInstance.startRetryNotifications();

        }
        else if (PROGRAM.statusReport) {
            L.log('Starting Daily status report sender...')
            CRONS.DailyReporter.exec(options, () => {
                L.log('DailyReporter finished its work beautifully, closing sql connection')
                options.dbInstance.close(function (err) {
                    if (err) {
                        L.log('Error while closing db connection');
                    }
                    L.log('Terminating cron')
                    process.exit(0)
                });
            })
        }
        else if (PROGRAM.ccIngestionNonPaytm) {
            let serviceInstance = new SERVICES.ccIngestionNonRu(options);
            serviceInstance.start();
            process.addListener('SIGUSR1', () => {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });
        }
        else if (PROGRAM.notificationReport) {
            L.log('Starting Daily notifications status report sender...')
            CRONS.NotificationReport.exec(options, () => {
                L.log('NotificationReport finished its work beautifully')
                options.dbInstance.close(function (err) {
                    if (err) {
                        L.log('Error while closing db connection');
                    }
                    L.log('Terminating cron')
                    process.exit(0)
                });
            })
        }
        else if (PROGRAM.notificationStatus) {
            L.log('Getting notifications status...');
            let serviceInstance = new SERVICES.notificationStatus(options);
            serviceInstance.start();

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });
        }
        else if (PROGRAM.billReminderNotification) {
            L.log('Starting bill reminder notifications service ...');
            let serviceInstance = new SERVICES.billReminderNotification(options);
            serviceInstance.start();

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });
        }
        else if (PROGRAM.heuristicCustomNotificationCreate) {
            L.log('Starting custom notifications service ...');
            let serviceInstance = new SERVICES.heuristicCustomNotificationCreateConsumer(options);
            serviceInstance.start();

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });
        }
        else if (PROGRAM.customNotificationCreate) {
            L.log('Starting custom notifications service ...');
            let serviceInstance = new SERVICES.normalCustomNotificationCreateConsumer(options);
            serviceInstance.start();

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });
        }
        else if (PROGRAM.billReminderNotificationRealtime) {
            L.log('Starting bill reminder notifications service ...');
            _.set(options, 'billReminderNotificationRealtime', true);
            let serviceInstance = new SERVICES.billReminderNotification(options);
            serviceInstance.start();

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });
        }
        else if (PROGRAM.billReminderNotificationNonRuRealtime) {
            L.log('Starting bill reminder notifications service ...');
            _.set(options, 'billReminderNotificationNonRuRealtime', true);
            let serviceInstance = new SERVICES.billReminderNotification(options);
            serviceInstance.start();

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });
        }
        else if (PROGRAM.billReminderNotificationNonRu) {
            L.log('Starting bill reminder notifications service non ru...');
            _.set(options, 'billReminderNotificationNonRu', true);
            let serviceInstance = new SERVICES.billReminderNotification(options);
            serviceInstance.start();

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });
        }
        else if (PROGRAM.planValidityRMQConsumer) {
            L.log('Starting plan validity RabbitMQ consumer ...');
            let serviceInstance = new SERVICES.planValidityRMQConsumer(options);
            serviceInstance.start();
            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });

        }
        else if (PROGRAM.allTransactionsConsumer) {
            L.log('Starting all transactions consumer ...');
            let serviceInstance = new SERVICES.allTransactionsConsumer(options);
            serviceInstance.start();

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });
        }
        else if (PROGRAM.billDuePublisher) {
            L.log('Starting Bill Due Publisher...')
            let billDuePublisherCron = new CRONS.BillDuePublisher(options);
            billDuePublisherCron.start();
        }
        else if (PROGRAM.billDuePrepaidPublisher) {
            const PREPAID_LOW_BALANCE_CONFIG = {
                jobName: 'PREPAID_LOW_BALANCE_BILLDUE',
                schedule: _.get(options.config, ['DYNAMIC_CONFIG', 'CRON_JOBS', 'PREPAID_LOW_BALANCE_BILLDUE', 'schedule'], '30 04 * * *')
            };

            const startPrepaidLowBalanceJob = () => {
                let billDuePrepaidPublisherCron = new CRONS.BillDuePrepaidPublisher(options);
                billDuePrepaidPublisherCron.start(() => { });
            };

            const jobs = [
                scheduleJob(
                    PREPAID_LOW_BALANCE_CONFIG.jobName,
                    PREPAID_LOW_BALANCE_CONFIG.schedule,
                    startPrepaidLowBalanceJob
                )
            ];

            const logJobSchedules = () => {
                jobs.forEach(job => {
                    L.info(
                        "PREPAID_LOW_BALANCE_BILLDUE Cron : ",
                        "Job Name:", job.name,
                        "next scheduled for:", job.nextInvocation().toString()
                    );
                });
            };

            // Initial log after 5 seconds
            setTimeout(logJobSchedules, 5000);
            // Log every minute thereafter
            setInterval(logJobSchedules, 60 * 1000);
        }
        else if (PROGRAM.billDuePublisherBatch1) {
            L.log('Starting Bill Due Publisher...')
            let billDuePublisherCron = new CRONS.BillDuePublisherBatch1(options);
            billDuePublisherCron.start();
        }
        else if (PROGRAM.billDuePublisherBatch2) {
            L.log('Starting Bill Due Publisher...')
            let billDuePublisherCron = new CRONS.BillDuePublisherBatch2(options);
            billDuePublisherCron.start();
        }
        else if (PROGRAM.billDuePublisherBatch3) {
            L.log('Starting Bill Due Publisher...')
            let billDuePublisherCron = new CRONS.BillDuePublisherBatch3(options);
            billDuePublisherCron.start();
        }
        else if (PROGRAM.billDuePublisherBatch4) {
            L.log('Starting Bill Due Publisher...')
            let billDuePublisherCron = new CRONS.BillDuePublisherBatch4(options);
            billDuePublisherCron.start();
        }
        else if (PROGRAM.oldBillPushToSaga) {
            L.log('Starting Old Bill Push To Saga...')
            let OldBillPushToSaga = new CRONS.OldBillPushToSaga(options);
            OldBillPushToSaga.start();
        }
        else if (PROGRAM.billDuePublisherEndOfDay) {
            L.log('Starting Bill Due Publisher End Of Day...')
            let billDuePublisherCron = new CRONS.BillDuePublisherEndOfDay(options);
            billDuePublisherCron.start();
        }
        else if (PROGRAM.billDuePublisherPaytmPostpaid) {
            L.log('Starting Bill Due Publisher...')
            let billDuePublisherPaytmPostpaid = new CRONS.BillDuePublisherPaytmPostpaid(options);
            billDuePublisherPaytmPostpaid.start();
        }
        else if (PROGRAM.airtelPrepaidBllDuePublisher) {
            L.log('Starting airtelPrepaidBllDuePublisher...')
            let airtelPrepaidBillDuePublisherCron = new CRONS.AirtelPrepaidBillDuePublisher(options);
            airtelPrepaidBillDuePublisherCron.start();
        }
        else if (PROGRAM.archivalRecords) {
            let ARCHIVAL_CRONS = () => {
                let serviceInstance = new CRONS.ArchivalRecords(options)
                serviceInstance.start(() => { })
            }

            let jobs = [
                scheduleJob('ARCHIVAL_CRONS', _.get(options.config, ['DYNAMIC_CONFIG', 'CRON_JOBS', 'ARCHIVALRECORDS', 'schedule'], '00 16 * * *'), ARCHIVAL_CRONS),
                //scheduleJob('ARCHIVAL_CRONS', MOMENT().add(10, 'second').toDate(), ARCHIVAL_CRONS)
            ];

            let logInvocations = () => {
                jobs.forEach(job => {
                    console.log("printing the job", job)
                    //L.info("Reminder Cron : ", "Job Name ", job.name, "next scheduled for ", job.nextInvocation().toString());
                })
            }
            setTimeout(logInvocations, 5000);
            setInterval(logInvocations, 60 * 1000);
        }
        else if (PROGRAM.planValidityNotification) {
            L.log('Sending plan validity notifications...');
            let serviceInstance = new SERVICES.planValidityNotification(options);
            serviceInstance.start().catch(err => {
                L.critical("plan validity notifications service has crashed. Please check this on priority.", err);
                process.exit(1);
            });
        }
        else if (PROGRAM.rechargeNudgeRechargeConsumer) {
            L.log("Starting recharge nudge consumer service to process recharge data");
            let instance = new CRONS.RechargeNudgeRechargeConsumer(options);
            instance.start();
        }
        else if (PROGRAM.rechargeNudgeServiceValidationConsumer) {
            L.log("Starting recharge nudge consumer service to process recharge data");
            let instance = new CRONS.RechargeNudgeServiceValidationConsumer(options);
            instance.start();
        }
        else if (PROGRAM.rechargeNudgeServiceRechargeConsumer) {
            L.log("Starting recharge nudge consumer service to process recharge data");
            let instance = new CRONS.RechargeNudgeServiceRechargeConsumer(options);
            instance.start();
        }
        else if (PROGRAM.rechargeNudgeValidationConsumer) {
            L.log("Starting recharge nudge consumer cron to process validation data and send notification");
            // setting mode for which consumer is to be started
            options.mode = PROGRAM.rechargeNudgeValidationConsumer
            let instance = new CRONS.RechargeNudgeValidationConsumer(options);
            instance.start();
        }
        else if (PROGRAM.recentBills) {
            let serviceInstance = new SERVICES.recentBills(options);
            L.log('Starting recent Bills data Service');

            try {
                // options.mongoDbInstance.connect(function (err, res) {
                //     if (err) {
                //         L.critical('Unable to connect to Mongo DB for recent bills Mode... ', err);
                //     } else {
                //         L.log('Connected to Mongo DB successfully for recent bills Mode... ');
                //     }
                serviceInstance.start();
                // });
            } catch (ex) {
                L.critical("Exception occured in recent bills Mode... ", ex);
                serviceInstance.start();
            }

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");

            });

        }
        else if (PROGRAM.syncReminder) {
            let serviceInstance = new SERVICES.reminderSync(options);
            L.log('Starting reminder sync Service');

            try {
                // options.mongoDbInstance.connect(function(err,res){
                //     if(err){
                //         L.critical('Unable to connect to Mongo DB for recent bills Mode... ',err );
                //     } else {
                //         L.log('Connected to Mongo DB successfully for recent bills Mode... ');
                //     }
                serviceInstance.start();
                // });
            } catch (ex) {
                L.critical("Exception occured in  syncReminder  ", ex);
            }

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");

            });
        }
        else if (PROGRAM.notificationService) {
            let serviceInstance = new SERVICES.notificationService(options);
            L.log('Starting notification service to manage notification...');
            serviceInstance.start();

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });
        }
        else if (PROGRAM.smsParsingCCBills) {
            let serviceInstance = new SERVICES.SmsParsingCCBills(options);
            L.log('Starting SMS Parsing consumer service to fetch CC Bills');
            serviceInstance.start();

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });
        }
        else if (PROGRAM.smsParsingCCBillsRealtime) {
            _.set(options, 'smsParsingCCBillsRealtime', true);
            let serviceInstance = new SERVICES.SmsParsingCCBills(options);
            L.log('Starting SMS Parsing consumer service to fetch CC Bills Realtime');
            serviceInstance.start();

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });
        }
        else if (PROGRAM.smsParsingCCBillsDwhRealtime) {
            _.set(options, 'smsParsingCCBillsDwhRealtime', true);
            let serviceInstance = new SERVICES.SmsParsingCCBills(options);
            L.log('Starting dwh realtime SMS Parsing consumer service to fetch CC Bills');
            serviceInstance.start();

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });
        }
        else if (PROGRAM.smsParsingFasTag) {
            let serviceInstance = new SERVICES.SmsParsingFastag(options);
            L.log('Starting SMS Parsing consumer service for FasTag');
            serviceInstance.start();

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });
        }
        else if (PROGRAM.emiDueConsumer) {
            let serviceInstance = new SERVICES.EmiDueConsumer(options);
            L.log('Starting EmiDueConsumer service to fetch Bills');
            serviceInstance.start();

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");

            });
        }
        else if (PROGRAM.updateRecentsConsumer) {
            let serviceInstance = new SERVICES.updateRecentsConsumer(options);
            L.log('Starting updateRecentsConsumer for updation recents partial payment');
            serviceInstance.start();
            process.addListener('SIGUSR1', function () {

                serviceInstance.suspendOperations();

                L.log("app :: Application terminated Gracefully");

            });
        }
        else if (PROGRAM.nonPaytmBillsConsumer) {
            let serviceInstance = new SERVICES.nonPaytmBills(options);
            L.log('Starting nonPaytmBillsConsumer for updating non Paytm records');
            serviceInstance.start();

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });

        }
        else if (PROGRAM.custIdRnMappingIngestion) {
            let serviceInstance = new CRONS.CustIdRnMappingIngestion(options);
            L.log('Starting custIdRnMapping ingestion to store mappings from csv');
            serviceInstance.start();

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });

        }
        else if (PROGRAM.emiDueCommonConsumer) {
            let serviceInstance = new SERVICES.EmiDueCommonConsumer(options);
            L.log('Starting EmiDueCommonConsumer service to fetch Bills');
            serviceInstance.start();
            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });
        }
        else if (PROGRAM.planValidityNotificationSubscriber && PROGRAM.planValidityNotificationSubscriber.length > 0) {
            L.log('Starting plan validity subcriber service...');

            let params = PROGRAM.planValidityNotificationSubscriber[0].split(',');
            options.consumerGroupId = params[0];

            let serviceInstance = new SERVICES.planValidityNotificationSubscriber(options);

            serviceInstance.start().catch(err => {
                L.critical("plan validity subcriber service has crashed. Please check this on proiority.", err);
                process.exit(1);
            });

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });

        }
        else if (PROGRAM.removeExpiredPlanValidity) {
            L.log('Starting remove Expired Plan Validity Cron...')

            let removeExpiredPlanValidity = new CRONS.RemoveExpiredPlanValidity(options);
            removeExpiredPlanValidity.start();

        }
        else if (PROGRAM.storeHistoricalRecords) {
            L.log('Storing historical records service started');
            let historicalService = new SERVICES.historicalRecords(options);
            historicalService.start();

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });
        }
        else if (PROGRAM.billReminderCylinderConsumer) {
            L.log('Bill reminder gas consumer service started');
            let billReminderCylinderConsumer = new SERVICES.BillReminderCylinderConsumer(options);
            billReminderCylinderConsumer.start();

            process.addListener('SIGUSR1', function () {

                billReminderCylinderConsumer.suspendOperations();

                L.log("app :: Application terminated Gracefully");

            });
        }
        else if (PROGRAM.operatorUpNotification) {
            L.log('notifocation to dropped customers service started');
            let operatorUpNotification = new SERVICES.OperatorUpNotification(options);
            operatorUpNotification.start();

            process.addListener('SIGUSR1', function () {

                operatorUpNotification.suspendOperations();

                L.log("app :: Application terminated Gracefully");

            });
        }

        else if (PROGRAM.updateUserConsentConsumer) {
            L.log('Starting updateUserConsentConsumer consumer');
            let serviceInstance = new SERVICES.updateUserConsentConsumer(options);
            serviceInstance.start();
            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });
        }
        else if (PROGRAM.syncAutomaticRecent) {
            L.log('Starting syncAutomaticRecent consumer');
            let serviceInstance = new SERVICES.syncAutomaticRecent(options);
            serviceInstance.start();

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });
        }
        else if (PROGRAM.smsParsingBillPayment) {
            options.saveForAnalyticsInCassandraAndKafka = true;
            let serviceInstance = new SERVICES.smsParsingBillPayment(options);

            L.log('Starting smsParsingBillPayment for updating records for those, we have recevied bill paid sms');

            serviceInstance.start();

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");

            });

        }
        else if (PROGRAM.smsParsingBillPaymentDwhRealTime) {

            _.set(options, 'telcoSmsParsingBillsDwhRealtime', true);
            options.saveForAnalyticsInCassandraAndKafka = true;

            let serviceInstance = new SERVICES.smsParsingBillPayment(options);

            L.log('Starting smsParsingBillPayment RealTime for updating records for those, we have recevied bill paid sms');

            serviceInstance.start();

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");

            });

        }
        else if (PROGRAM.ccsmsParsingBillPayment) {

            let serviceInstance = new SERVICES.ccsmsParsingBillPayment(options);

            L.log('Starting cc-smsParsingBillPayment for updating records for those, we have recevied bill paid sms');

            serviceInstance.start();

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");

            });

        }
        else if (PROGRAM.electricitysmsParsingBillPayment) {
            options.saveForAnalyticsInCassandraAndKafka = true;
            let serviceInstance = new SERVICES.ElectricitySMSParsingBillPayment(options);

            L.log('Starting electricity-smsParsingBillPayment for updating records for those, we have recevied bill paid sms');

            serviceInstance.start();

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");

            });

        }
        else if (PROGRAM.electricitysmsParsingBillPaymentDwhRealtime) {
            options.saveForAnalyticsInCassandraAndKafka = true;
            options.electricityDwhRealTimeSmsParsing = true;
            let serviceInstance = new SERVICES.ElectricitySMSParsingBillPayment(options);

            L.log('Starting electricity-smsParsingBillPayment dwh realTime for updating records for those, we have recevied bill paid sms');

            serviceInstance.start();

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");

            });
        }
        else if (PROGRAM.dthsmsParsingBills) {
            options.saveForAnalyticsInCassandraAndKafka = true;
            // options.dthDwhRealTimeSmsParsing = true;
            let serviceInstance = new SERVICES.DthSmsParsingBills(options);

            L.log('Starting dth-smsParsingBillPayment for updating records for those, we have recevied bill');

            serviceInstance.start();

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");

            });

        }
        else if (PROGRAM.rentsmsParsingBillPayment) {
            let serviceInstance = new SERVICES.RentSmsParsing(options);

            L.log('Starting rent-smsParsingBillPayment for updating records for those, we have recevied bill paid sms');

            serviceInstance.start();

            //     process.addListener('SIGUSR1', function(){
            //         serviceInstance.suspendOperations();
            //         L.log("app :: Application terminated Gracefully");
            //    });
        }
        else if (PROGRAM.smsParsingLoanEmi) {
            let serviceInstance = new SERVICES.SmsParsingLoanEmi(options);
            L.log('Starting SmsParsingLoanEmi for updating Loan Emi bills, consuming the Loans SMSs and power reminders');
            serviceInstance.start();
            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");

            });
        }
        else if (PROGRAM.prepaidHistoricRecords) {

            L.log('Storing prepaid historic records service started');
            let serviceInstance = new SERVICES.prepaidHistoricRecords(options);
            serviceInstance.start();
        }
        else if (PROGRAM.paytmPostpaid) {

            L.log('Storing paytmPostpaid records service started');
            let serviceInstance = new SERVICES.paytmPostpaid(options);
            serviceInstance.start();
            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");

            });
        }
        else if (PROGRAM.personalLoan) {

            L.log('Storing personalLoan records service started');
            let serviceInstance = new SERVICES.personalLoan(options);
            serviceInstance.start();
            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");

            });
        }
        else if (PROGRAM.realtimeSmsParsingPrepaid) {

            L.log('Realtime sms parsing prepaid service started');
            let serviceInstance = new SERVICES.realtimeSmsParsingPrepaid(options);
            serviceInstance.start();
            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");

            });
        }
        else if (PROGRAM.realtimeSmsParsingPostpaid) {

            L.log('Realtime sms parsing postpaid service started');
            let serviceInstance = new SERVICES.realtimeSmsParsingPostpaid(options);
            serviceInstance.start();
            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");

            });
        }
        else if (PROGRAM.notificationFallbackConsumer) {

            L.log('NotificationFallbackConsumer records service started');
            let serviceInstance = new SERVICES.notificationFallBackConsumer(options);
            serviceInstance.start();
            process.addListener('SIGUSR1', () => {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });
        }
        else if (PROGRAM.csvCron) {


            let COMMON_CAAS = () => {
                let serviceInstance = new CSVUpdate(options)
                serviceInstance.start(() => { })
            }

            let jobs = [
                // scheduleJob('AirtelTVCSV',_.get(options.config,['DYNAMIC_CONFIG','CRON_JOBS','AirtelTVCSV','schedule'],'0 0 1 * *'),AirtelTVCSV) , 
                scheduleJob('COMMON_CAAS', '*/5 * * * * *', COMMON_CAAS),
            ]


            let logInvocations = () => {
                jobs.forEach(job => {
                    L.info("csvCron : ", "Job Name ", job.name, "next scheduled for ", job.nextInvocation().toString())
                })
            }

            setTimeout(logInvocations, 5000)

            setInterval(logInvocations, 60 * 1000)
        }
        else if (PROGRAM.fallbackCustomerIdIngester) {
            let FallbackCustomerIdIngester = () => {
                let serviceInstance = new CRONS.FallbackCustomerIdIngester(options);
                serviceInstance.executeFlow(() => { });
            };

            let jobs = [
                scheduleJob('FallbackCustomerIdIngester', _.get(options.config, ['DYNAMIC_CONFIG', 'CRON_JOBS', 'FallbackCustomerIdIngester', 'schedule'], '0 22 * * *'), FallbackCustomerIdIngester)
            ];

            let logInvocations = () => {
                jobs.forEach(job => {
                    L.info("FallbackCustomerIdIngester Cron : ", "Job Name ", job.name, "next scheduled for ", job.nextInvocation().toString());
                })
            };

            setTimeout(logInvocations, 5000);
            setInterval(logInvocations, 60 * 1000);
        }
        else if (PROGRAM.oldBillDuePublisher) {
            let OLD_BILLDUEPUBLISHER = () => {
                let serviceInstance = new CRONS.OldBillDuePublisher(options)
                serviceInstance.start(() => { })
            }

            let jobs = [
                scheduleJob('OLD_BILLDUEPUBLISHER', _.get(options.config, ['DYNAMIC_CONFIG', 'CRON_JOBS', 'OLD_BILLDUEPUBLISHER', 'schedule'], '00 04 * * *'), OLD_BILLDUEPUBLISHER)
                //scheduleJob('OLD_BILLDUEPUBLISHER', MOMENT().add(10, 'second').toDate(), OLD_BILLDUEPUBLISHER)
            ];

            let logInvocations = () => {
                jobs.forEach(job => {
                    console.log("printing the job", job)
                    L.info("Reminder Cron : ", "Job Name ", job.name, "next scheduled for ", job.nextInvocation().toString());
                })
            }

            setTimeout(logInvocations, 5000);
            setInterval(logInvocations, 60 * 1000);
        }
        else if (PROGRAM.reminderScheduler) {

            let BILLGENPUBLISHER = () => {
                let serviceInstance = new CRONS.BillGenPublisher(options)
                serviceInstance.start(() => { })
            }

            let jobs = [
                scheduleJob('BILLGENPUBLISHER', _.get(options.config, ['DYNAMIC_CONFIG', 'CRON_JOBS', 'BILLGENPUBLISHER', 'schedule'], '0 0 * * *'), BILLGENPUBLISHER),
                //scheduleJob('OLD_BILLDUEPUBLISHER', MOMENT().add(10, 'second').toDate(), OLD_BILLDUEPUBLISHER)
            ];

            let logInvocations = () => {
                jobs.forEach(job => {
                    console.log("printing the job", job)
                    L.info("Reminder Cron : ", "Job Name ", job.name, "next scheduled for ", job.nextInvocation().toString());
                })
            }

            setTimeout(logInvocations, 5000);
            setInterval(logInvocations, 60 * 1000);
        }
        else if (PROGRAM.ccPublisher) {
            L.info('Publisher mode enabled ... ')

            try {
                // options.mongoDbInstance.connect(function (err) {
                //     if (err) {
                //         L.critical('Unable to connect to Mongo DB for Publisher Mode... ', err);
                //     } else {
                //         L.log('Connected to Mongo DB successfully for Publisher Mode... ');

                let batchId = PROGRAM.ccPublisher

                options.batchId = batchId;

                let publisherManager = new SERVICES.publisherManager(options);
                let operators = ["visa_hdfcbank"];// _.get(options.config, ['PUBLISHER_CONFIG', 'PUBLISHER', batchId], ["cc_hdfc bank"]);
                L.log('Running for operators: ', operators)
                operators.forEach(operator => {
                    let tableName = _.get(options.config, ['OPERATOR_TABLE_REGISTRY', operator], null)
                    if (tableName && publisherManager.createPublisherForOperator(tableName, operator, true)) {

                        publisherManager.startPublisherForOperator(operator);
                        //    let CCPUBLISHER = () => {
                        //     publisherManager.startPublisherForOperator(operator);
                        // }

                        // let jobs = [
                        //     scheduleJob('CCPUBLISHER', _.get(options.config, ['DYNAMIC_CONFIG', 'CRON_JOBS', 'CCPUBLISHER', 'schedule'], '* * * * *'), CCPUBLISHER)
                        // ];

                        // let logInvocations = () => {
                        //     jobs.forEach(job => {
                        //         L.info("CC Publisher Cron : ", "Job Name ", job.name, "next scheduled for ", job.nextInvocation().toString());
                        //     })
                        // }

                        // setTimeout(logInvocations, 5000);
                        // setInterval(logInvocations,60 * 1000);
                    }
                    else {
                        L.error('No table configured for the operator: ', operator);
                    }
                })
                process.addListener('SIGUSR1', function () {
                    publisherManager.stopAllPublishers();
                    L.log("app :: Application terminated Gracefully");
                });
                //     }
                // });
            } catch (ex) {
                L.critical("Exception occured in Publisher Mode... ", ex);
            }
        }
        else if (PROGRAM.ccIngestionKafka) {


            let isRunning = false;
            let CC_Ingestion_Kafka = () => {
                if (isRunning == true) {
                    L.error("Previos instance of cron already running skipping for today")
                    return
                }
                isRunning = true;
                let serviceInstance = new CCIngestionKafka(options)
                serviceInstance.start(() => {
                    isRunning = false
                })
            }
            CC_Ingestion_Kafka();
            // let jobs = [
            //     scheduleJob('CC_INGESTION_KAFKA',_.get(options.config,['DYNAMIC_CONFIG','CRON_JOBS','CC_Ingestion_Kafka','schedule'],'0 0 * * *'),CC_Ingestion_Kafka) ,
            // ]

            // let logInvocations = ()=>{
            //     jobs.forEach(job=>{
            //         L.info("csvCron : ", "Job Name ",job.name , "next scheduled for ",job.nextInvocation().toString())
            //     })
            // }

            // setTimeout(logInvocations, 5000)

            // setInterval(logInvocations, 60 * 1000)
        }
        else if (PROGRAM.ccIngestion) {


            let isRunning = false;
            let CC_Ingestion = () => {
                if (isRunning == true) {
                    L.error("Previos instance of cron already running skipping for today")
                    return
                }
                isRunning = true;
                let serviceInstance = new CCIngestion(options)
                serviceInstance.start(() => {
                    isRunning = false
                })
            }

            let jobs = [
                scheduleJob('CC_INGESTION', _.get(options.config, ['DYNAMIC_CONFIG', 'CRON_JOBS', 'CC_Ingestion', 'schedule'], '0 0 * * *'), CC_Ingestion),
            ]

            let logInvocations = () => {
                jobs.forEach(job => {
                    L.info("csvCron : ", "Job Name ", job.name, "next scheduled for ", job.nextInvocation().toString())
                })
            }

            setTimeout(logInvocations, 5000)

            setInterval(logInvocations, 60 * 1000)
        }
        else if (PROGRAM.airtelKafkaPublisher) {
            let serviceInstance = new AirtelKafkaPublisher(options)
            serviceInstance.start()
            process.addListener('SIGUSR1', () => {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });
        }
        else if (PROGRAM.airtelBillFetchConsumer) {
            let serviceInstance = new SERVICES.airtelBillFetchConsumer(options);
            L.log('Starting AirtelBillFetchConsumer service to fetch Bills');
            serviceInstance.start();
            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });
        }
        else if (PROGRAM.customNotificationCron) {

            let serviceInstance = new CustomNotifications(options);

            const CUSTOM_NOTIFICATION = () => {
                if (serviceInstance.isRunning) {
                    console.log("Previous instance still running, skipping this run.");
                    serviceInstance.isCheckInvalid((isInvalid) => {
                        if (!isInvalid) {
                            console.log("Record is  not invalid yet ");
                            return;

                        }
                        else {
                            serviceInstance.fileName = "";
                            serviceInstance.isRunning = false;
                            console.log("Processing ended as file got invalid");
                            return;
                        }
                    });
                    return;

                }

                if (!serviceInstance.isInitialized) {
                    serviceInstance.initialize((error) => {
                        if (error) {
                            console.error("Initialization failed:", error);
                            return;
                        }
                        // Once initialized, start the processing loop
                        serviceInstance.isRunning = true;
                        serviceInstance.startProcessing(() => {
                            serviceInstance.isRunning = false;
                            serviceInstance.fileName = "";
                            console.log("Processing ended");
                        });
                    });
                } else {
                    serviceInstance.isRunning = true;
                    serviceInstance.startProcessing(() => {
                        serviceInstance.fileName = "";
                        serviceInstance.isRunning = false;
                        console.log("Processing ended from  else");
                    });
                }
            };

            let jobs = [
                scheduleJob('CUSTOM_NOTIFICATION', _.get(options.config, ['DYNAMIC_CONFIG', 'CRON_JOBS', 'CUSTOM_NOTIFICATIONS', 'schedule'], '*/5 * * * * *'), CUSTOM_NOTIFICATION),
            ]

            let logInvocations = () => {
                jobs.forEach(job => {
                    L.info("csvCron : ", "Job Name ", job.name, "next scheduled for ", job.nextInvocation().toString())
                })
            }

            setTimeout(logInvocations, 2000);
            setInterval(logInvocations, 5000);
        }
        else if (PROGRAM.heuristicCustomNotificationCron) {
            let serviceInstance = new HeuristicCustomNotifications(options)
            let HEURISTIC_CUSTOM_NOTIFICATION = () => {
                if (serviceInstance.isRunning) {
                    console.log("Previous instance still running, skipping this run.");
                    serviceInstance.isCheckInvalid((isInvalid) => {
                        if (!isInvalid) {
                            console.log("Record is  not invalid yet ");
                            return;

                        }
                        else {
                            serviceInstance.fileName = "";
                            serviceInstance.isRunning = false;
                            console.log("Processing ended as file got invalid");
                            return;
                        }
                    });
                    return;

                }

                if (!serviceInstance.isInitialized) {
                    serviceInstance.initialize((error) => {
                        if (error) {
                            console.error("Initialization failed:", error);
                            return;
                        }
                        // Once initialized, start the processing loop
                        serviceInstance.isRunning = true;
                        serviceInstance.startProcessing(() => {
                            serviceInstance.isRunning = false;
                            serviceInstance.fileName = "";
                            console.log("Processing ended");
                        });
                    });
                } else {
                    serviceInstance.isRunning = true;
                    serviceInstance.startProcessing(() => {
                        serviceInstance.fileName = "";
                        serviceInstance.isRunning = false;
                        console.log("Processing ended from  else");
                    });
                }
            };

            let jobs = [
                scheduleJob('HEURISTIC_CUSTOM_NOTIFICATION', _.get(options.config, ['DYNAMIC_CONFIG', 'CRON_JOBS', 'HEURISTIC_CUSTOM_NOTIFICATIONS', 'schedule'], '*/5 * * * * *'), HEURISTIC_CUSTOM_NOTIFICATION),
            ]


            let logInvocations = () => {
                jobs.forEach(job => {
                    L.info("csvCron : ", "Job Name ", job.name, "next scheduled for ", job.nextInvocation().toString())
                })
            }

            setTimeout(logInvocations, 2000)

            setInterval(logInvocations, 5000)
        }
        else if (PROGRAM.airtelPrepaidIngest) {

            let isRunning = false;
            let AIRTEL_PREPAID_INGEST = () => {
                if (isRunning == true) {
                    L.error("Previos instance of cron already running skipping for today")
                    return
                }
                isRunning = true
                let serviceInstance = new AIRTELCSVINGEST(options)
                serviceInstance.start(() => {
                    isRunning = false;
                })
            }

            let jobs = [
                // scheduleJob('AirtelTVCSV',_.get(options.config,['DYNAMIC_CONFIG','CRON_JOBS','AirtelTVCSV','schedule'],'0 0 1 * *'),AirtelTVCSV) , 
                scheduleJob('AIRTEL_PREPAID_INGEST', _.get(options.config, ['DYNAMIC_CONFIG', 'CRON_JOBS', 'AIRTEL_PREPAID_INGEST', 'schedule'], '0 0 * * *'), AIRTEL_PREPAID_INGEST),
            ]


            let logInvocations = () => {
                jobs.forEach(job => {
                    L.info("AIRTEL_PREPAID_INGEST : ", "Job Name ", job.name, "next scheduled for ", job.nextInvocation().toString())
                })
            }

            setTimeout(logInvocations, 5000)

            setInterval(logInvocations, 60 * 1000)
        }
        else if (PROGRAM.userScoreCSVIngest) {

            let isRunning = false;
            let USER_SCORE_CSV_INGEST = () => {
                if (isRunning == true) {
                    L.error("Previos instance of cron already running skipping for today");
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_CSV_INGESTOR", 'ERROR:INSTANCE_OF_CRON_ALREADY_RUNNING']);
                    return
                }
                isRunning = true
                let serviceInstance = new USERSCORECSVINGEST(options)
                serviceInstance.start(() => {
                    isRunning = false;
                })
            }

            // USER_SCORE_CSV_INGEST();
            let jobs = [
                scheduleJob('USER_SCORE_CSV_INGEST', _.get(options.config, ['DYNAMIC_CONFIG', 'CRON_JOBS', 'USER_SCORE_CSV_INGEST', 'schedule'], '0 0 * * *'), USER_SCORE_CSV_INGEST),
            ]


            let logInvocations = () => {
                jobs.forEach(job => {
                    L.info("USER_SCORE_CSV_INGEST : ", "Job Name ", job.name, "next scheduled for ", job.nextInvocation().toString())
                })
            }

            setTimeout(logInvocations, 5000)

            setInterval(logInvocations, 60 * 1000)
        }
        else if (PROGRAM.whatsappCustomerWhitelistCron) {

            let isRunning = false;
            let WHATSAPP_CUSTOMER_WHITELIST_INGESTOR = () => {
                if (isRunning == true) {
                    L.error("Previos instance of cron already running skipping for today");
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:WHATSAPP_CUSTOMER_WHITELIST_INGESTOR", 'ERROR:INSTANCE_OF_CRON_ALREADY_RUNNING']);
                    return
                }
                isRunning = true
                let serviceInstance = new WHATSAPPCUTOMERWHITELIST(options)
                serviceInstance.start(() => {
                    isRunning = false;
                })
            }

            // USER_SCORE_CSV_INGEST();
            let jobs = [
                scheduleJob('WHATSAPP_CUSTOMER_WHITELIST_INGESTOR', _.get(options.config, ['DYNAMIC_CONFIG', 'CRON_JOBS', 'WHATSAPP_CUSTOMER_WHITELIST_INGESTOR', 'schedule'], '0 5 * * *'), WHATSAPP_CUSTOMER_WHITELIST_INGESTOR),
            ]


            let logInvocations = () => {
                jobs.forEach(job => {
                    L.info("WHATSAPP_CUSTOMER_WHITELIST_INGESTOR: ", "Job Name ", job.name, "next scheduled for ", job.nextInvocation().toString())
                })
            }

            setTimeout(logInvocations, 5000)

            setInterval(logInvocations, 60 * 1000)
        }
        else if (PROGRAM.notifyRejectedBills) {
            let serviceInstance = new SERVICES.notifyRejectedBills(options);
            L.log('Starting notifyRejectedBills service to send notification for rejected bills');
            serviceInstance.start();
            process.on('uncaughtException', function (err) {
                L.error('Caught exception: ' + err);
            })
            process.on('unhandledRejection', function (reason, p) {
                L.error('Unhandled Rejection at: Promise', p, 'reason:', reason);
            })
            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });
        }
        else if (PROGRAM.activePaytmUsersConsumer) {
            let serviceInstance = new SERVICES.activePaytmUsersConsumer(options);
            L.log('Starting ActivePaytmUsers Consumer.......');
            serviceInstance.start();
            process.on('uncaughtException', function (err) {
                L.error('Caught exception: ' + err);
            })
            process.on('unhandledRejection', function (reason, p) {
                L.error('Unhandled Rejection at: Promise', p, 'reason:', reason);
            })
            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });
        }
        else if (PROGRAM.checkActiveUsersConsumer) {
            let serviceInstance = new SERVICES.checkActiveUsersConsumer(options);
            L.log('Starting CheckActiveUsers Consumer.......');
            serviceInstance.start();
            process.on('uncaughtException', function (err) {
                L.error('Caught exception: ' + err);
            })
            process.on('unhandledRejection', function (reason, p) {
                L.error('Unhandled Rejection at: Promise', p, 'reason:', reason);
            })
            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });

        }
        else if (PROGRAM.cronUserScoreIngestion) {
            let serviceInstance = new SERVICES.cronUserScoreIngestionConsumer(options);
            L.log('Starting cron user score ingestion consumer.......');
            serviceInstance.start();
            process.on('uncaughtException', function (err) {
                L.error('Caught exception: ' + err);
            })
            process.on('unhandledRejection', function (reason, p) {
                L.error('Unhandled Rejection at: Promise', p, 'reason:', reason);
            })
            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });

        }
        else if (PROGRAM.whatsappCustomerWhitelistConsumer) {
            let serviceInstance = new SERVICES.cronWhatsAppWhitelistCustomerConsumer(options);
            L.log('Starting cron whatsapp customer whitelist ingestion consumer.......');
            serviceInstance.start();
            process.on('uncaughtException', function (err) {
                L.error('Caught exception: ' + err);
            })
            process.on('unhandledRejection', function (reason, p) {
                L.error('Unhandled Rejection at: Promise', p, 'reason:', reason);
            })
            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });

        }
        else if (PROGRAM.userScoreingestionConsumer) {
            let serviceInstance = new SERVICES.userScoreIngestionConsumer(options);
            L.log('Starting userScoreingestionConsumer Consumer.......');
            serviceInstance.start();
            process.on('uncaughtException', function (err) {
                L.error('Caught exception: ' + err);
            })
            process.on('unhandledRejection', function (reason, p) {
                L.error('Unhandled Rejection at: Promise', p, 'reason:', reason);
            })
            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });

        }
        else if (PROGRAM.cronUserScoreIngestionConsumer) {
            let serviceInstance = new SERVICES.cronUserScoreIngestionConsumer(options);
            L.log('Starting cron user score ingestion consumer.......');
            serviceInstance.start();
            process.on('uncaughtException', function (err) {
                L.error('Caught exception: ' + err);
            })
            process.on('unhandledRejection', function (reason, p) {
                L.error('Unhandled Rejection at: Promise', p, 'reason:', reason);
            })
            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });

        }
        else if (PROGRAM.generalSmsParser) {
            L.info('GenericSmsParsing mode enabled ... ')

            let params = (PROGRAM.generalSmsParser?.[0] || '').split(',');
            options.categoryId = parseInt(params[0]) || 0;
            options.saveForAnalyticsInCassandraAndKafka = true;
            let serviceInstance = new SERVICES.generalSmsParser(options);

            /* 
                provide comma separated topic names if you want to run this service for a specific topic. example:
                    node dist/app.js --generalSmsParser topic1,topic2
                    node dist/app.js --generalSmsParser topic1
                or you can execute normally
                    node dist/app.js --generalSmsParser 
            */
            //let kafkaTopic = typeof (PROGRAM.generalSmsParser) == "string" ? PROGRAM.generalSmsParser.split(",") : null;
            serviceInstance.start();
            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application generalSmsParser terminated Gracefully");
            });
        }
        else if (PROGRAM.reminderConsentConsumer) {
            L.info('Starting reminderConsentConsumer');
            let serviceInstance = new SERVICES.ReminderConsentConsumer(options);
            serviceInstance.start();
            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });
        }
        else if (PROGRAM.expiredCAPublisher) {
            let serviceInstance = new SERVICES.expiredCAPublisher(options);
            L.log('Starting expiredCAPublisher.......');
            serviceInstance.start();
            process.on('uncaughtException', function (err) {
                L.error('Caught exception: ' + err);
            })
            process.on('unhandledRejection', function (reason, p) {
                L.error('Unhandled Rejection at: Promise', p, 'reason:', reason);
            })
            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });
        }
        else if (PROGRAM.s3CustomerUpdatesProcessor) {
            L.log('Starting S3 Customer Updates Processor cron...');
            let serviceInstance = new S3CustomerUpdatesProcessor(options);
            serviceInstance.start();

            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });

            process.on('uncaughtException', function (err) {
                L.error('Caught exception: ' + err);
            });

            process.on('unhandledRejection', function (reason, p) {
                L.error('Unhandled Rejection at: Promise', p, 'reason:', reason);
            });
        }
        else if (PROGRAM.customerLastActivityUpdateConsumer) {
            // Start the customer updates consumer service
            L.info('Starting Customer Updates Consumer service...');

            let serviceInstance = new SERVICES.cronCustomerUpdatesConsumer(options);
            serviceInstance.start();
        }

        else if (PROGRAM.whatsappNotificationFallbackConsumer) {
            let serviceInstance = new SERVICES.whatsappNotificationFallbackConsumer(options);
            L.log('Starting whatsappNotificationFallbackConsumer.......');
            serviceInstance.start();
            process.on('uncaughtException', function (err) {
                L.error('Caught exception: ' + err);
            })
            process.on('unhandledRejection', function (reason, p) {
                L.error('Unhandled Rejection at: Promise', p, 'reason:', reason);
            })
            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");
            });
        }
        else if (PROGRAM.smsParsingValidator) {

            L.log('smsParserValidator mode enabled ... ');
            let serviceInstance = new SERVICES.smsParsingValidator(options);
            serviceInstance.start();
            process.on('uncaughtException', (err) => {
                L.critical('uncaughtException:: ', err.stack || err);
            });
            process.addListener('SIGUSR1', function () {
                serviceInstance.suspendOperations();
                L.log("app :: Application terminated Gracefully");

            });
        }
        else {  //Starting as a web-api server
            L.info('Started as a API Server...');
            let APP = EXPRESS();

            /*
                Initiating controller's object
            */
            let CONTROLLER_OBJECT = new CONTROLLERS(options);

            /*
                Setting http and https hits' pool size
            */
            _.set(HTTP, 'globalAgent.maxSockets', 5000);
            _.set(HTTPS, 'globalAgent.maxSockets', 5000);

            APP.use(BODYPARSER.json({ limit: '100mb' }));
            APP.use(BODYPARSER.urlencoded({ extended: false }));
            APP.use(EXPRESS.static('public'));
            APP.use((err, req, res, next) => {
                if (err instanceof SyntaxError && err.status === 400 && 'body' in err) {    // This check makes sure this is a JSON parsing issue, but it might be

                    L.error(err);                                                     // coming from any middleware, not just body-parser:

                    return res.sendStatus(400); // Bad request

                }

                next();
            });

            try {
                // options.mongoDbInstance.connect(function (err, res) {
                //     if (err) {
                //         L.critical('Unable to connect to Mongo DB for Digital Reminder System... ', err);
                //     } else {
                //         L.log('Connected to Mongo DB successfully for Digital Reminder System... ');
                //     }
                /*
                    creating the web server
                */
                CONTROLLER_OBJECT.initializeServer(function (error) {
                    if (error) {
                        L.critical("Error while initializeServer", error);
                    }
                    else {
                        HTTP.createServer(APP)
                            .on('error', function (error) {
                                L.critical('Error in creating HTTP Server', error);
                                process.exit(1);
                            })
                            .listen(PORT, function () {
                                L.info('Digital Reminder System listening on port - ', PORT);
                            });
                    }
                });

                // });
            } catch (ex) {
                L.error('Exception Occured while connecting to Mongo DB for Digital Reminder System... ', ex);
                CONTROLLER_OBJECT.initializeServer(function (error) {
                    if (error) {
                        L.critical("Error while initializeServer", error);
                    } else {
                        HTTP.createServer(APP)
                            .on('error', function (error) {
                                L.critical('Error in creating HTTP Server', error);
                                process.exit(1);
                            })
                            .listen(PORT, function () {
                                L.info('Digital Reminder System listening on port - ', PORT);
                            });
                    }
                });
            }



            //Initialize Router and bind our app with various routes
            let router = new ROUTER(APP, { controller: CONTROLLER_OBJECT });
            router.bindRoutes();
        }
        deferred.resolve();
        return deferred.promise;
    }).catch(err => {
        L.critical("Service has crashed. Please check this on priority.", err);
        process.exit(1);
    });

