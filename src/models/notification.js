import _ from 'lodash'
let L = null;
import moment from 'moment';
import EncryptionDecryptioinHelper from '../lib/EncryptionDecryptioinHelper';

class Notification {
    constructor(options) {
        this.L = options.L;
        L = options.L;
        this.config = options.config;
        this.status_list = _.get(this.config, ['NOTIFICATION', 'status']);
        this.dbInstance = options.dbInstance;
        this.EncryptionDecryptioinHelper = new EncryptionDecryptioinHelper(options);
    }

    getNotifications(cb, status, startTime, batchSize, categoryId) {
        let self = this,
            categoryClause = categoryId ? ' AND category_id = ? ' : '',
            query = 'SELECT * from notification WHERE send_at >= ? AND send_at <= NOW()' + categoryClause + ' AND status = ? LIMIT ?',
            queryParams = [];
        queryParams.push(startTime);
        if (categoryId) queryParams.push(categoryId);
        queryParams.push(status);
        queryParams.push(batchSize);
        L.info("getting notification for ", self.dbInstance.format(query, queryParams));
        self.dbInstance.exec(function (err, data) {
            if (err) {
                L.error('getNotifications: ', 'error occurred while getting notification data from DB: ', query, err);
            }
            cb(err, data);
        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
    }

    getNotificationsByJobId(jobId, batchSize) {
        let self = this,
            query = 'SELECT * from notification WHERE job_id = ? ORDER BY id DESC LIMIT ?',
            queryParams = [jobId, batchSize];
        L.info("getting notification for ", self.dbInstance.format(query, queryParams));

        return new Promise((resolve, reject) => self.dbInstance.exec(function (err, data) {
            if (err) {
                L.error('getNotificationsByJobId: ', 'error occurred while getting notification data from DB: ', query, err);
            }
            resolve(data);
        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams));
    }

    getRetryNotifications(cb, status, startTime, batchSize) {
        let self = this,
            query = 'SELECT * from notification WHERE send_at >= ? AND send_at <= NOW() AND status IN (?) LIMIT ?',
            queryParams = [
                startTime,
                status,
                batchSize
            ];

        L.info("getRetryNotifications", "getting notification for ", self.dbInstance.format(query, queryParams));
        self.dbInstance.exec(function (err, data) {
            if (err) {
                L.error('getRetryNotifications: ', 'error occurred while getting retry notification data from DB: ', query, err);
            }
            cb(err, data);
        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
    }

    getSentNotifications(cb, status, timeInterval, batchSize) {
        let self = this,
            query = 'SELECT * from notification WHERE sent_at <= ? AND status = ? LIMIT ?';
        self.dbInstance.exec(function (err, data) {
            if (err) {
                L.error('getSentNotifications: ', 'error occurred while getting notification data from DB: ', query, err);
            }
            cb(err, data);
        }, 'DIGITAL_REMINDER_SLAVE', query, [timeInterval, status, batchSize]);
    }

    getNotificationsCount(cb, categoryId, templateType, partitions, date, endDate) {
        let self = this,
            query = 'SELECT \
                        product_id, ' + this._getSumQuery() + ', \
                        category_id, type \
                        from notification \
                    WHERE \
                        send_at >= ? AND \
			send_at < ? AND \
                        category_id = ? AND \
                        type = ? AND \
                        id >= ? AND \
                        id < ? \
                        group by product_id',
            params = [date, endDate, categoryId, templateType, partitions.start, partitions.end];

        L.log("Notification::getNotificationsCount", self.dbInstance.format(query, params));
        self.dbInstance.exec(function (err, data) {
            if (err) {
                L.critical('Notification::getNotificationsCount', 'error occurred while getting notification data from DB: ', query, err);
            }
            cb(err, data);
        }, 'DIGITAL_REMINDER_SLAVE', query, params);
    }

    _getSumQuery() {
        let statuses = this.config.NOTIFICATION.notificationReportStatus
        let keys = _.keys(statuses)
        let query = []
        for (let status in statuses) {
            query.push('count(case when status = ' + statuses[status] + ' then 1 else null end) as ' + status)
        }
        return query.join(", ")
    }

    getPartitionRangeByDate(cb, date) {
        let self = this,
            query = 'SELECT id from notification WHERE send_at >= ? limit 1';
        self.dbInstance.exec(function (err, data) {
            if (err) {
                L.error('getSentNotifications: ', 'error occurred while getting notification data from DB: ', query, err);
            }
            cb(err, data);
        }, 'DIGITAL_REMINDER_SLAVE', query, [date]);
    }

    checkPriority(cb, params) {
        params.recipient = (params.recipient).toString();
        let self = this,
            query = `SELECT * from notification
                        where type = ?
                        and recipient = ?
                        and category_id = ?
                        and priority < ?
                        and send_at > ? `;
        self.dbInstance.exec(function (err, data) {
            if (err) {
                L.error('checkPriority::', 'error occurred while getting notification data from DB: ', query, err);
            }
            cb(err, data);
        }, 'DIGITAL_REMINDER_SLAVE', query, [params.type, params.recipient, params.category_id, params.priority, params.last_ref_time]);
    }

    checkCondition(cb, condition, params) {
        let self = this,
            query = `SELECT * from notification
                        where recipient = ?
                        and send_at > ?
                        and (${condition})`,
            arr =['select','insert', 'update', 'drop', 'delete', 'sleep', 'count', 'floor', 'concat', 'schema','from', 'mysql', 'rand', 'database'];
        
        for(let key in arr){
            if (condition.toLowerCase().includes(arr[key])){
                L.error('checkCondition::', 'sql injection found in condtion ');   
                let error = { error  : 'SQL_INJECTION',
                              message : `sql injection found in condtion ${condition}`};
                return cb(error);
            }
        }

        self.dbInstance.exec(function (err, data) {
            if (err || !(data)) {
                L.error('checkCondition::', 'error occurred while getting checkCondition data from DB: ', query, err);
            }
            cb(err, data);
        }, 'DIGITAL_REMINDER_SLAVE', query, [params.recipient, params.last_ref_time]);
    }


    checkConditionV2(cb, condition, params) {
        params.recipient = (params.recipient).toString();
        let self = this,
            query = `SELECT * from notification
                        where recipient = ?
                        and send_at > ?`,
            queryParams = [params.recipient, params.last_ref_time];

        if(typeof condition === 'object' && !_.isEmpty(condition)){
            for(let key in condition){
                query += ` and ${key} = ?`;  
                queryParams.push(condition[key]);                  
            }
        }else {           
            L.error('checkConditionV2::', 'error occurred due to condition not in json format ');            
            let error = { error  : 'error occurred due to condition not in json format ',
                          message : 'error occurred due to condition not in json format ' };
            return cb(error); 
        }

        L.info("checkConditionV2 query ", self.dbInstance.format(query,queryParams));
        self.dbInstance.exec(function (err, data) {
            if (err || !(data)) {
                L.error('checkConditionV2::', 'error occurred while getting checkConditionV2 data from DB: ', query, err);
            }
            cb(err, data);
        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
    }

    cancelNotification(cb, idsToCancel) {
        let self = this,
            query = 'update notification set status = ? where id in (' + idsToCancel + ')';
        self.dbInstance.exec(function (err, data) {
            if (err || !(data)) {
                L.error('cancelNotification::', 'error occurred while getting checkCondition data from DB: ', query, err);
            }
            cb(err, data);
        }, 'DIGITAL_REMINDER_MASTER', query, [_.get(self.status_list, 'CANCELED', null)]);
    }

    getStatusForNotificationCreation(params){
        var self=this;
        let sendAt = moment(params.send_at).format('YYYY-MM-DD');
        let product_id = _.get(params, 'product_id', null);
        let template_id = params.template_id;

        let status =
            _.get(self.config, ['DYNAMIC_CONFIG', 'DISABLE_NOTIFY', sendAt, product_id + '-' + template_id],
                _.get(self.config, ['DYNAMIC_CONFIG', 'DISABLE_NOTIFY', sendAt, product_id],
                    _.get(self.config, ['DYNAMIC_CONFIG', 'DISABLE_NOTIFY', 'ALWAYS', product_id + '-' + template_id],
                        _.get(self.config, ['DYNAMIC_CONFIG', 'DISABLE_NOTIFY', 'ALWAYS', product_id],
                            _.get(self.config, ['NOTIFICATION', 'status', 'PENDING'], 0)))));
        
        /**
         * In case if the status needs to be added apart from the pending status.
         * In case of Reschedule, we need to create notification with status as 7.
         * So if in params there is key notificationStatus, then the status gets overriden by the previous status variable
         */

        if (params.notificationStatus) {
            status = params.notificationStatus;
        }
        return status;
    }

    createNotification(cb, record) {
        let self = this;

        let encryptedRecord = _.cloneDeep(record);
        let isEncrypted = 0;

        let params = self.EncryptionDecryptioinHelper.getNotificationParamsForCCBP(record);

        _.set(encryptedRecord, 'data', JSON.stringify(_.get(record, 'data', null)));

        if(self.EncryptionDecryptioinHelper.isWhitelistedForCC(params.service, params.paytype, params.customerId)){
            _.set(encryptedRecord, 'recharge_number', self.EncryptionDecryptioinHelper.encryptData(_.get(record, 'recharge_number', null)));
            _.set(encryptedRecord, 'data', self.EncryptionDecryptioinHelper.encryptData(_.get(encryptedRecord, 'data', null)));
            _.set(encryptedRecord, 'recipient', self.EncryptionDecryptioinHelper.encryptData(_.get(encryptedRecord, 'recipient', null)));
            isEncrypted = 1;
        }
        _.set(encryptedRecord, 'is_encrypted', isEncrypted);

        return self.createNotificationDB(cb, encryptedRecord);
    }

    createNotificationDB(cb, params) {
        let self = this;
        let status= self.getStatusForNotificationCreation(params)
        let query = 'insert into notification (type,template_id,recipient,source_id,category_id,recharge_number,product_id,data,max_retry_count, retry_interval, priority, send_at, status, is_encrypted ) ' +
            'values (?,?,?,?,?,?,?,?,?,?,?,?,?,?)';
        let queryParams = [
            params.type, params.template_id, params.recipient, params.source_id, params.category_id, _.get(params, 'recharge_number', null), 
            Number(_.get(params, 'product_id', null)), JSON.stringify(params.data), params.max_retry_count, params.retry_interval, 
            params.priority, params.send_at, status, _.get(params, 'is_encrypted', 0)
        ]
        self.L.log('notification::createNotification::', `executing query - ${self.dbInstance.format(query, queryParams)}`);
        self.dbInstance.exec(function (err, data) {
            if (err || !(data)) {
                L.critical('createNotification::', 'error occurred while getting checkCondition data from DB: ', query, err);
            }
            cb(err, data, status);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }

    createNotificationForRetry(cb, params) {
        let self = this;
        let status= self.getStatusForNotificationCreation(params);
        let query = 'insert into notification (type, template_id, recipient, source_id, category_id, recharge_number, product_id, data, max_retry_count, retry_interval, priority, send_at, status, retry_count, error_msg, job_id) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)';
        let queryParams = [
            params.type, params.template_id, params.recipient, params.source_id, params.category_id, _.get(params, 'recharge_number', null),
            Number(_.get(params, 'product_id', null)), JSON.stringify(_.get(params, 'data', null)), params.max_retry_count, params.retry_interval, 
            params.priority, params.send_at, status, params.retry_count, _.get(params, 'error_msg', null), params.job_id
        ]

        self.L.log('notification::createNotificationForRetry::', `executing query ${self.dbInstance.format(query, queryParams)}`);
        self.dbInstance.exec(function (err, data) {
            if (err || !(data)) {
                L.critical('createNotificationForRetry::', 'error occurred while getting checkCondition data from DB: ', query, err);
            }
            cb(err, data, status);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }

    updateNotification(cb, fields, whereCondition, params) {
        let self = this;
        if (fields.length === 0 || fields.length > params.length || whereCondition.length === 0) {
            L.error('updateNotification: ', 'Input parameters are wrong');
            cb('Input parameters are wrong');
        } else {
            self.buildUpdateQuery((query) => {
                // L.log('updateNotification','query : ',self.dbInstance.format(query,params));
                self.dbInstance.exec(function (err, data) {
                    if (err) {
                        L.critical('updateNotification: ', 'error occurred while updating notification data: ', query, err);
                    }else{
                        // L.log('updateNotification','Record updated on time : ',new Date().getTime(),self.dbInstance.format(query,params));
                    }
                    cb(err, data);
                }, 'DIGITAL_REMINDER_MASTER', query, params);
            }, fields, whereCondition);
        }
    }

    buildUpdateQuery(callback, fields, whereCondition) {
        let query = "UPDATE notification SET ";

        fields.forEach(val => {
            query += val + ' = ?,';
        });

        query = query.replace(/,\s*$/, "");
        query += ' WHERE ' + whereCondition + ';';
        callback(query);
    }

    getNotificationsByCondition(cb, whereCondition, queryParams) {
        let self = this;
        let query = `SELECT * FROM notification WHERE ${whereCondition}`;
        self.dbInstance.exec((err, data) => {
            if (err) {
                self.L.error('getNotificationsByCondition::', `err occurred for query ${self.dbInstance.format(query, queryParams)}`, err);
                return cb(err);
            }
            cb(err, data);
        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams)
    }

    deleteNotification(cb, whereCondition, queryParams) {
        let self = this;
        let query = `DELETE FROM notification WHERE ${whereCondition}`;
        self.dbInstance.exec((err, data) => {
            if (err) {
                self.L.error('deleteNotification::', `error occurred for query ${self.dbInstance.format(query, queryParams)}`, err);
            }
            cb(err);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }

    checkIfDbRecordIsEncrypted(record) {
        return _.get(record, 'is_encrypted', false);
    }
}

export default Notification
