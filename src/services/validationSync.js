import CATALOGVERTICALRECHARGE from '../models/catalogVerticalRecharge';
import BILLS from '../models/bills';
import recentBillLibrary from '../lib/recentBills';
import reminderFlowManager from '../lib/reminderFlowManager';
import utility from '../lib';
import BillsLibrary from '../lib/bills'
import MOMENT from 'moment';
import ASYNC from 'async';
import RecentsLayerLib from '../lib/recentsLayer';
import _, { reject } from 'lodash';
import VALIDATOR from 'validator';
import OS from 'os';
import BILLS_SUBSCRIBER from './billSubscriber';
import DigitalCatalog from '../lib/digitalCatalog';
import digitalUtility from 'digital-in-util'
import Q from 'q';
import DroppedTransactions from "../models/droppedTransactions";
import nonPaytmBills from "../models/nonPaytmBills";
import { resolve } from 'path';
import BillFetchAnalytics from '../lib/billFetchAnalytics.js'
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';
import BillPush from '../lib/billPush';
import EncryptionDecryptioinHelper from '../lib/EncryptionDecryptioinHelper'

import PrepaidFlowManager from '../lib/prepaidFlowManager.js';
import Logger from '../lib/logger';

class ValidationSync {
    constructor(options) {
        let self = this;
        this.L = options.L;
        this.options = options;
        this.nonPaytmBillsModel  = new nonPaytmBills(options)
        this.config = options.config;
        this.dbInstance = options.dbInstance;
        this.blockedPaytype = _.get(this.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'BLOCKED_PAYTYPES'], []);;
        this.allowedPrepaidDthOperator = _.get(this.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'ALLOWED_DTH_OPERATOR'], []);
        this.billSubscriber = new BILLS_SUBSCRIBER(options);
        this.catalogVerticalRecharge = new CATALOGVERTICALRECHARGE(options);
        this.options.billsModel = this.bills = new BILLS(options);
        this.commonLib = new utility.commonLib(options);
        this.billsLib = new BillsLibrary(options);
        this.activePidLib = options.activePidLib;
        this.consentData = {};
        this.recentsLayerLib = new RecentsLayerLib(options);
        this.recentBillLibrary = new recentBillLibrary(options);
        this.operators = _.get(this.config, ['RECENT_BILL_CONFIG', 'OPERATORS'], {});
        this.bills_operator_table = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
        this.recent_bills_operators = this.recentBillLibrary._initRecentBillSpecificData(this.bills_operator_table, this.operators);
        this.infraUtils = options.INFRAUTILS;
        this.cvrReloadInterval = _.get(this.config, 'COMMON.CVR_RELOAD_INTERVAL', 86400000);
        this.reminderUtils = new digitalUtility.ReminderUtils();
        this.allowedServiceToSaveOldDueDate = _.get(this.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'ALLOWED_SERVICES'], ['electricity']);
        this.greyScaleEnv = options.greyScaleEnv;
        this.reminderFlowManager = new reminderFlowManager(options);
        this.rechargeNumberAlreadySeen = [];
        //this.includeCustomerIds= [1380125451,26033279,1763650058,40458956,963545,3973717,109199840];
        this.setVarFromDynamicConfig();
        this.droppedTransactions  = new DroppedTransactions(options)
        this.operator_circle_productId_map = {};
        this.flush_operator_circle_productId_map();
        this.digitalCatalog = new DigitalCatalog(options);
        this.billFetchAnalytics = new BillFetchAnalytics(options);
        this.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        this.BillPush = new BillPush(options);
        this.billPushServiceRegistration = _.get(this.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'BILL_PUSH_SERVICE_REGISTRATION'], null);
        this.billPushServiceRegistrationOperatorsAllowed = _.get(this.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'BILL_PUSH_OPERATORS'], ['electricity']);
        this.lowBalancePrepaidElectricityAllowedOperators = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'PREPAID_ENABLED_OPERATORS', 'OPERATORS'], []);
        this.prepaidElectricityNotificationThreshold = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'COMMON', 'NOTIFICATION_THRESHOLD'], 2000);
        this.prepaidFlowManager = new PrepaidFlowManager(options);
        this.disableUpmsRegChannelIdMap = _.get(this.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'DISABLE_CHANNEL_ID_UPMS_REG_MAP'], {});
        this.OPERATOR_LIST = ['vodafone idea', 'jio', 'airtel'];
        this.OPERATOR_DEFAULT_AMOUNT = {'vodafone idea': 299, 'jio': 239, 'airtel': 239};
        this.OPERATOR_LIST.forEach((operator_name)=>{
            this.OPERATOR_DEFAULT_AMOUNT[operator_name] = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', operator_name, 'DEFAULT_AMOUNT'], 299);
        });
        this.encryptionDecryptionHelper = new EncryptionDecryptioinHelper(options);
        this.logger = new Logger(options);
    }

    setVarFromDynamicConfig() {
        let self = this;
        this.allowedServiceToSaveOldDueDate = _.get(this.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'ALLOWED_SERVICES'], ['electricity']);
        this.blockedPaytype = _.get(this.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'BLOCKED_PAYTYPES'], []);
        this.allowedPrepaidDthOperator = _.get(this.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'ALLOWED_DTH_OPERATOR'], []);
        this.disableValidationSync = _.get(this.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'DISABLE_FOR_ALL'], false);
        this.skipNotification = _.get(this.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'SKIP_SENDING_NOTIFICATION'], false);
        this.kafkaBatchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'VALIDATION_SYNC', 'BATCHSIZE'], 2) : 500;
        this.includedServiceList = _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_MONTHLY_PAYMENT', 'COMMON', 'INCLUDE_SERVICE'], ['rent payment']);
        this.operators = _.get(this.config, ['RECENT_BILL_CONFIG', 'OPERATORS'], {});
        this.bills_operator_table = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
        this.recent_bills_operators = this.recentBillLibrary._initRecentBillSpecificData(this.bills_operator_table, this.operators);
        this.billPushServiceRegistration = _.get(this.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'BILL_PUSH_SERVICE_REGISTRATION'], null);
        this.billPushServiceRegistrationOperatorsAllowed = _.get(this.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'BILL_PUSH_OPERATORS'], ['electricity']);
        this.lowBalancePrepaidElectricityAllowedOperators = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'PREPAID_ENABLED_OPERATORS', 'OPERATORS'], []);
        this.prepaidElectricityNotificationThreshold = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'COMMON', 'NOTIFICATION_THRESHOLD'], 2000);
        this.disableUpmsRegChannelIdMap = _.get(this.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'DISABLE_CHANNEL_ID_UPMS_REG_MAP'], {});
        this.operatorUpAllowedCategoryOperatorMap = _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_UP_NOTIFICATION', 'ALLOWED', 'CATEGORY_OPERATOR_MAP'], {});
        this.operatorUpAmountAllowedCategory = _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_UP_NOTIFICATION', 'ALLOWED', 'AMOUNT_CATEGORY'], []);
        this.OPERATOR_LIST = ['vodafone idea', 'jio', 'airtel'];
        this.OPERATOR_DEFAULT_AMOUNT = {'vodafone idea': 299, 'jio': 239, 'airtel': 239};
        this.OPERATOR_LIST.forEach((operator_name)=>{
            this.OPERATOR_DEFAULT_AMOUNT[operator_name] = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', operator_name, 'DEFAULT_AMOUNT'], 299);
        });

        setTimeout(() => {
            self.L.log(`setVarFromDynamicConfig`, `Updating service params from dynamic config..`)
            self.setVarFromDynamicConfig()
        }, 15 * 60 * 1000);
    }
    
    start() {
        let self = this;
        self.L.log('ValidationSync:: Starting service');
        self.setConsentData((error) => {
            if (_.isEmpty(self.consentData) || error) {
                self.L.error("There was an error in setting Consent Data", error);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:ERROR', 'TYPE:CVR_DATA']);
            } else {
                self.L.log('setConsentData:: Success');
                self.L.log("start", " Validation Sync service started");

                self.L.log('start', 'Going to configure Kafka');
                self.configureKafka(function (error) {
                    if (error) {
                        self.L.critical('ValidationSync :: start', 'unable to configure kafka', error);
                        process.exit(0);
                    }
                    else {
                        self.L.log('ValidationSync :: start', 'Kafka Configured successfully !!');
                        setInterval(self.setConsentData.bind(self), self.cvrReloadInterval, (err) => {
                            if (err) {
                                self.L.critical('_refreshProductData : error while re-loading CVR data', err);
                            } else {
                                self.L.log('_refreshProductData : reload CVR data ');
                            }
                        });
                    }
                });
            }
        });
    }

    setConsentData(callback) {
        let self = this;
        let whereQuery = null;
        if (Object.keys(self.consentData).length) {
            whereQuery = `updated_at > "${MOMENT().subtract(2, 'day').subtract(1, 'hour').format("YYYY-MM-DD HH:mm:ss")}"`;
        }
        self.catalogVerticalRecharge.getCvrData(function (error, data) {
            try {
                if (error || !data) callback(error);
                else {
                    data.forEach(function (row) {
                        if (row && row.attributes) {
                            try {
                                let remindable = JSON.parse(row.attributes).remindable;
                                self.consentData[row.product_id] = remindable == _.get(self.config, 'COMMON.USER_CONSENT_REQUIRED', 2) ? true : false;
                            } catch(error){
                                self.L.critical('setConsentData',`Error while parsing attributes for product_id:${row && row.product_id},row: ${JSON.stringify(row)}`);
                            }
                        }
                    });
                    callback();
                }
            } catch (Exception) {
                callback(Exception);
            }
        }, whereQuery);
    }

    configureKafka(done) {
        /**
         * maintain this sequence
         * 1) Initialize all publisher
         * 2) Initialize all consumers
         */
        let self = this;
        ASYNC.waterfall([
            next => {
                self.L.log('configureKafkaPublisher', 'Going to initialize Kakfa Publisher');
                return self._configureKafkaPublisher(next);
            },
            next => {
                self.L.log('configureKafka', `Going to initialize Kakfa Consumer for FFR Validation topics : ${_.get(self.config.KAFKA, 'SERVICES.VALIDATION_SYNC.VALIDATION_TOPICS', []).join(',')}`);

                // Initialize validation sync consumer.
                self.kafkaBillFetchConsumer = new self.infraUtils.kafka.consumer({
                    "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.RECHARGE.HOSTS'),
                    "groupId": "validation-sync",
                    "topics": _.get(self.config.KAFKA, 'SERVICES.VALIDATION_SYNC.VALIDATION_TOPICS'),
                    "id": 'validationSyncConsumer_' + OS.hostname() + '_' + process.pid,
                    "fromOffset": "earliest",
                    "autoCommit": false,
                    "batchSize": self.kafkaBatchSize
                });

                self.kafkaBillFetchConsumer.initConsumer(self.execSteps.bind(self), (error) => {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:ERROR', 'TYPE:KAFKA_CONSUMER', 'TOPIC:VALIDATION_SYNC_TOPICS']);
                    }
                    if (!error)
                        self.L.log("configureKafka", "consumer of topic : VALIDATION_TOPICS Configured");
                    return next(error);
                });
            }
        ], function (error) {
            if (error) {
                self.L.critical('configureKafka', 'Could not initialize Kafka', error);
            }
            return done(error);
        });
    }

    _configureKafkaPublisher(cb) {
        let
            self = this;

        ASYNC.parallel([
            next => {
                /**
                 * Kafka Publisher to publish bill fetch to Automatic
                 */
                self.kafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.AUTOMATIC_SYNC.HOSTS
                });
                self.kafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:ERROR', 'TYPE:KAFKA_PRODUCER', 'TOPIC:AUTOMATIC_SYNC']);
                    }
                    return next(error);
                });
            },
            next => {
                /**
                 * Kafka Publisher to publish bill fetch to Reminder Pipeline
                 */
                self.kafkaBillFetchPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.REMINDER_BILLFETCH_PIPELINE.HOSTS
                });
                self.kafkaBillFetchPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:ERROR', 'TYPE:KAFKA_PRODUCER', 'TOPIC:REMINDER_BILL_FETCH']);
                    }
                    return next(error);
                });
            },
            next => {
                /**
                 * Kafka publisher to publish events to CT publisher pipeline 
                 */
                 self.ctKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.CT_EVENTS_PUBLISHER.HOSTS
                });
                this.ctKafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:ERROR', 'TYPE:KAFKA_PRODUCER', 'TOPIC:CT_EVENTS']);
                    }
                    return next(error)
                });
            },
            next => {
                  self.nonPaytmKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.NON_PAYTM_RECORDS.HOSTS
                });
                this.nonPaytmKafkaPublisher.initProducer('high', function (error) {
                  return next(error);
                });
            },
            next => {
                /**
                 * Kafka publisher to publish events of UMPS_SUBCRIPTION
                 */
                self.upmsPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.UPMS_PUBLISHER.HOSTS
                });
                this.upmsPublisher.initProducer('high', function (error) {
                    if (error) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:ERROR', 'TYPE:UPMS_PUBLISHER', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                self.billFetchKafkaRealtime = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.REMINDER_BILLFETCH_PIPELINE_REALTIME.HOSTS
                });
                self.billFetchKafkaRealtime.initProducer('high', function (error) {
                    if (error){
                        self.L.critical('error in initialising billFetchKafka Producer :: ', error);
                    }
                    else{
                        self.L.log("VALIDATION SYNC :: billFetchKafka realtime KAFKA PRODUCER STARTED....");
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:ERROR', 'TYPE:KAFKA_PRODUCER', 'TOPIC:REMINDER_BILLFETCH_REALTIME']);
                    }
                    return next(error);
                });
            }
        ], function (error) {
            if (error) {
                L.critical('_configureKafkaPublisher', 'error - ', error);
            }
            return cb(error);
        });
    }

    execSteps(records) {
        let self = this,
            chunkSize = _.get(self.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'CHUNKSIZE'], 50),
            currentPointer = 0, lastMessage;

        let startTime = new Date().getTime();

        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
            self.kafkaBillFetchConsumer._pauseConsumer();
        } else {
            self.L.critical('execSteps', `No valid kafka records found. Received records-`, records);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:ERROR', 'TYPE:INVALID_RECORDS']);
            return;
        }

        self.L.log('execSteps:: ', `Processing ${records.length} validation records !!`);

        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer + chunkSize));
                currentPointer += chunkSize;
                self.processBatch(nextChunk, () => {
                    setTimeout(() => {
                        callback();
                    }, 200);
                });
            },
            (err) => {
                self.kafkaConsumerChecks.findOffsetDuplicates("ValidationSync", records);

                self.kafkaBillFetchConsumer.commitOffset(lastMessage, (error) => {
                    if (error) {
                        self.L.critical('execSteps::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                        process.exit(0);
                    }
                    else {
                        self.L.log('execSteps::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    }
                    
                    let endTime = new Date().getTime();
                    let executionTime = (endTime - startTime) / 1000;      //in seconds
                    executionTime = Math.round(executionTime);
                    self.L.log('execSteps::', 'per batchSize record Execution time :', executionTime, 'seconds ',records.length);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:VALIDATION_SYNC", "TIME_TAKEN:" + executionTime]);
                    
                    
                    // Resume consumer now
                    if (self.greyScaleEnv) {
                        setTimeout(function () {
                            self.kafkaBillFetchConsumer._resumeConsumer();
                        }, _.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'VALIDATION_SYNC', 'BATCH_DELAY'], 100));
                    } else {
                        self.kafkaBillFetchConsumer._resumeConsumer();
                    }
                });
            }
        );
    }

    flush_operator_circle_productId_map() {
        let self = this;
        setInterval(() => {
            self.operator_circle_productId_map = {};
        }, 120 * 60 * 1000);
    }

    processBatch(records, done) {
        let self = this;
        // let processedRecords = records.map((record) => { self.processRecord(record); });
        let processedRecords = [];
        for (let itrRecords = 0; itrRecords < records.length; itrRecords++) {
            processedRecords.push(self.processRecord(records[itrRecords]));
        }
        Promise.all(processedRecords)
            .then(() => {
                self.rechargeNumberAlreadySeen = []; // flushing recharge numbers seen array of the batch
                return done();
            })
            .catch(() => {
                return done();
            });
    }

    async processRecord(payLoad) {
        let self = this;
        try {

            utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:TRAFFIC', `PARTITION:${payLoad.partition}`, `TOPIC:${payLoad.topic}`]);

            let record = await self.convertKafkaPayloadToRecord(payLoad);
            let channelId = utility._getCustomChannelId(_.get(record, 'validationChannel', null));
            let ignoreChannelIdFlag = self.shouldIgnoreChannelId(_.get(record, 'service', null), channelId);


            if (!ignoreChannelIdFlag && ((self.billPushServiceRegistration && self.billPushServiceRegistration.includes(_.get(record, 'service', null))) || (self.billPushServiceRegistrationOperatorsAllowed && self.billPushServiceRegistrationOperatorsAllowed.includes(_.get(record, 'operator', null))))) {
                self.BillPush.pushToRegistrationProcess(self, record, payLoad, 'validationSync');
            }

            utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:TRAFFIC', `CHANNEL_ID:${channelId}`, `IGNORE_BILLPUSH:${ignoreChannelIdFlag}`, `TOPIC:${payLoad.topic}`]);

            let errorResponse = await self.validateKafkaPayload(record);
            if (errorResponse) {
                // Invalid record count send to promethius
                
                if(!record) {
                    record = payLoad;
                }
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:VALIDATION_FAILURE']);
                self.logger.error(`processRecord:: Invalid Record record: error:${errorResponse} `, record, _.get(record, 'service', null));
                return await Promise.resolve();
            }
            let recents_blacklist_user = _.get(self.config, ['DYNAMIC_CONFIG', 'MNP_VALIDATION_BLOCK', 'BLACKLIST', 'CUST_IDS'], []);
            if(recents_blacklist_user.includes(record.customerId)){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:BLACKLIST_CUST_ID', "OPERATOR:" + record.operator]);
                let errorResponse = `Blacklisted customer id`;
                self.logger.error(`processRecord:: Invalid Record for blacklist custid record: error:${errorResponse} `, record, _.get(record, 'service', null));
                await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record, "FULL_BILL", null), errorResponse);
                return await Promise.resolve();
            }
            if((record.paytype == 'prepaid' && record.service =='dth' && !(self.allowedPrepaidDthOperator.indexOf(_.toLower(record.operator))>-1)) || (record.paytype == 'prepaid' && record.service !='dth')){
                return await self.processPrepaidRecord(record, payLoad);
            }
        
            let tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', record.productId], null) ||
                _.get(self.config, ['OPERATOR_TABLE_REGISTRY', record.operator], null) || _.get(self.config, ['SERVICE_TABLE_REGISTRY', record.service], null);

            if (!tableName) {
                let errorResponse = `No table associated with ${record.operator}`;
                await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record, "FULL_BILL", null), errorResponse);
                self.L.error(`processRecord:: ${record.operator} not migrated`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:TABLE_NOT_FOUND', "OPERATOR:" + record.operator]);
                return await Promise.resolve();
            }

            let activePid = self.activePidLib.getActivePID(record.productId);
            let remindable = self.consentData[activePid];

            
            _.set(record, 'tableName', tableName);
            _.set(record, 'remindable', remindable);
            if (_.get(record, 'status', null) != _.get(this.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5)) {
                _.set(record, 'status', _.get(this.config, 'COMMON.bills_status.BILL_FETCHED', 4));
            }
            _.set(record, 'skipNotification', true);
            _.set(record, 'updateInRecents', false);
            
            if (record.paytype == 'credit card') {
                return await self.processCreditCardRecord(record);
            }

            let recordsFound = await self.getRecordsFromDb(record);

            if (record.validationChannel == 'SUBS 1' && [3, 4].indexOf(_.get(record, 'is_automatic', 0)) == -1) {
                _.set(record, 'is_automatic', 1);
            }

            if (_.get(record, 'isValidPrepaidElectricityOperator', false) === true) {
                let prepaidRecord = _.cloneDeep(record);
                const response = await self.handleElectricityRecord(prepaidRecord);
                if(!_.get(response, 'proceedWithPostpaidFlow', false)) {
                    let nonPaytmKafkaPayload = self.getNonPaytmDeletePayload(prepaidRecord);
                    await this.publishNonPaytmEvents(nonPaytmKafkaPayload);
                    let ct_payload = _.cloneDeep(prepaidRecord);
                    await this.publishCtEvents(ct_payload);
                    return Promise.resolve();
                }
            }

            let nextBillFetchDate = self.getNextBillFetchDate(record , (_.get(record, 'is_automatic',null) || _.get(record, 'is_automatic_diffCustId',null) ));

            _.set(record, 'nextBillFetchDate', nextBillFetchDate);

            if(record.skipBillCheck)
            {
                record.nextBillFetchDate =  MOMENT().add(1, 'days').format('YYYY-MM-DD HH:mm:ss');
                if(recordsFound){
                    let dbData = _.get(record,'dbData',null);
                    dbData = dbData[0];
                    const validationTimeStamp = _.get(record, 'validationTimeStamp');

                    let dbBillDate =   _.get(dbData,'bill_date',null) ? MOMENT(_.get(dbData,'bill_date',null)).utc().format('YYYY-MM-DD HH:mm:ss') : null,
                        dbDueDate = _.get(dbData,'due_date',null) ? MOMENT(_.get(dbData,'due_date',null)).utc().format('YYYY-MM-DD HH:mm:ss') : null,
                        comparingDbDueDate = dbDueDate ? MOMENT(dbDueDate).add(3,'days').format('YYYY-MM-DD HH:mm:ss') : null,
                        currentTime = MOMENT(validationTimeStamp).format('YYYY-MM-DD HH:mm:ss') || MOMENT().format('YYYY-MM-DD HH:mm:ss'),
                        isWithinRange,
                        newStatus = null,
                        dbExtra = JSON.parse(_.get(dbData, 'extra', {}));
                    if(dbBillDate && comparingDbDueDate && currentTime >= dbBillDate && currentTime <= comparingDbDueDate)
                        isWithinRange = true;
                    else
                        isWithinRange = false;

                    // console.log("printing the dbBillDate :: ", dbBillDate)
                    // console.log("printing the dbDueDate :: ", dbDueDate);
                    // console.log("printing the comaparingDbDueDate :: ", comparingDbDueDate);
                    // console.log("printing the currentTime :: ", currentTime);
                    
                    if(_.get(dbData,'bill_date',null) == null && _.get(dbData,'due_date',null) != null){
                        self.updateBills(record,record.tableName,newStatus);
                    }
                    if(isWithinRange){
                        newStatus = 14;
                        await self.updateBills(record, record.tableName, newStatus);
                    }
                    else if(comparingDbDueDate && currentTime > comparingDbDueDate){
                        await self.updateBills(record, record.tableName, newStatus);
                    }
                    _.set(record, 'dbBillSource', _.get(dbExtra, 'billSource', null));

                    if (_.get(record, 'recordFoundOfSameCustId', null)) {
                        let errorResponse = "record Found Of Same CustId";
                        await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record,"FULL_BILL", "RU"), errorResponse);
                        return await Promise.resolve();
                    }

                }
                
                delete record.dbData;
                record.billsData = self.getBillsData(record);
                record.billsData.commonStatus = _.get(self.config, 'COMMON.bills_status.BILL_NOT_FOUND', 6);
                await self.createBill(record);
                let nonPaytmDeletePayload = self.getNonPaytmDeletePayload(record);
                return await self.publishNonPaytmEvents(nonPaytmDeletePayload);
            }

            /*
            1 if cust_id present in payload
               1.1.1 cust_id present in db -> is_automatic will be that came through payload
               1.1.2 if its 1 -> update 1 for that cust, 2 for rest
               1.1.3 if its 0 -> update 0 for all
 
               1.2.1 cust_id not present in db, is_automatic will be of other cust_id (from getRecordsFromDb fn)
               1.2.2 if SUBS_1=true, then is_automatic=1 for only that cust_id, for others update it as 2
               1.2.3 if SUBS_1=false, then check if other cust_ids (from step 1.2.1), if they have non-zero is_automati and 1/2 then 2 if 3/4 then 4, 
                     that means this cust_id must have is_automatic as 2 or 4.
               1.2.4 update this cust_id as is_automatic=2 or 4, for rest leave is_automatic as it is.
            */
            let dbRecord = _.get(record,'dbData',null);
            dbRecord = dbRecord ? dbRecord[0]:null;

            if(dbRecord!=null){
                let oldDueDate = _.get(dbRecord,'due_date',null)?MOMENT(_.get(dbRecord,'due_date',null)).utc().startOf('day') : null;
                this.L.log('billSubscriber::_dumpInSQL', 'Old Due Date', oldDueDate);
                let newDueDate = record.dueDate ? MOMENT(record.dueDate).utc().startOf('day') : null;
                this.L.log('billSubscriber::_dumpInSQL', 'new Due Date', newDueDate);
                newDueDate = newDueDate ? newDueDate.subtract(_.get(this.config, ['DYNAMIC_CONFIG', 'REMIND_ME_LATER_CONFIG', 'RU', 'DEFAULT_DIFF_DAYS'], 5), 'days'):null;
                if(oldDueDate && newDueDate && newDueDate.isAfter(oldDueDate,'day')) {
                    this.L.log('billSubscriber::_dumpInSQL', 'Due date difference is more than threshold, resetting remind later date');
                    _.set(record, 'resetRemindLaterDate', true);
                }
            }

            await self.compareAndUpdateDbData(record, recordsFound);
            let ct_payload = _.cloneDeep(record);
            await self.publishCtEvents(ct_payload);
            let service = _.get(record, 'service', null);

            // if (record.updateInRecents && !self.includedServiceList.includes(service)) {
            //     await self.updateBillsInRecent(record);
            // }
            
            let nonPaytmDeletePayload = self.getNonPaytmDeletePayload(record);
            await self.publishNonPaytmEvents(nonPaytmDeletePayload);

            if (self.skipNotification) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:SKIP_NOTIFY', 'TYPE:SKIP_DYMANICALLY']);
                self.L.log(`processRecord:: Notification Disabled from Dynamic Config` + record.debugKey);
            } else if (record.skipNotification) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:SKIP_NOTIFY', 'TYPE:SKIP_RECORD_FLAG']);
                self.L.log(`processRecord:: Skiping Notification because skipNotification flag was set = true by flow for ` + record.debugKey);
            } else {
                self.L.log(`processRecord`, `Going for publishInKafka for ${record.debugKey}`)
                await self.publishInKafka(record);
            }
        }
        catch (err) {
            self.L.error('processRecord:: ', err);
        }

        return await Promise.resolve();
    }

    getNonPaytmDeletePayload(record){
        let nonPaytmKafkaPayload = {
            customerId: record.customerId,
            service: record.service,
            paytype: record.paytype,
            productId: record.productId,
            operator: record.operator,
            rechargeNumber: record.rechargeNumber,
            dbEvent: 'delete'
        }
        return nonPaytmKafkaPayload;
    }

    async convertKafkaPayloadToRecord(payLoad) {
        let self = this;
        
        try {
            payLoad = JSON.parse(_.get(payLoad, 'value', null));
        } catch (error) {
            if (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:ERROR', 'TYPE:RECORD_PARSING']);
                self.L.critical('convertKafkaPayloadToRecord', `Invalid Kafka record received`, payLoad);
                return;
            }
        }
        
        let rechargeNumber = _.get(payLoad, 'userData_recharge_number', ''),
            bankAttributes,bankName, cardNetwork;

        // //ensuring unique recharge numbers per batch processed
        // if(self.rechargeNumberAlreadySeen.includes(rechargeNumber)){
        //     self.L.log("ValidationSync :: Skipping duplicate recharge number ", rechargeNumber)
        //     return
        // } else {
        //     self.rechargeNumberAlreadySeen.push(rechargeNumber);
        // }
        let paytype = _.toLower(_.get(payLoad, 'productInfo_paytype', ''));
        let service = _.toLower(_.get(payLoad, 'productInfo_service', ''));
        let operator = _.toLower(_.get(payLoad, 'productInfo_operator', ''));

        if (self.blockedPaytype.indexOf(_.toLower(paytype)) > -1) {
            let errorResponse = "Paytype is blocked";
            await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(payLoad, "FULL_BILL", null), errorResponse);
            return;
        }

        let customerId = _.get(payLoad, 'customerInfo_customer_id', 0);
        let productId = _.get(payLoad, 'catalogProductID', 0);
        let categoryId = _.get(payLoad, 'productInfo_category_id', 0);
        let amount = utility.getFilteredAmount(_.get(payLoad, 'customerDataResponse.currentBillAmount', '0'));
        let dueDate = utility.getFilteredDate(_.get(payLoad, 'customerDataResponse.billDueDate', null)).value;


        if((paytype == 'prepaid' && service =='dth' && !(self.allowedPrepaidDthOperator.indexOf(_.toLower(operator))>-1)) || (paytype == 'prepaid' && service !='dth') || (paytype == 'credit card' && _.get(payLoad, 'currentGw', null) != 'euronetpostpaid') || self.includedServiceList.includes(service))
        {
            amount = utility.getFilteredAmount(_.get(payLoad, 'userData_amount', '0'));
            dueDate = MOMENT(); 
        }

        let billDate = utility.getFilteredDate(_.get(payLoad, 'customerDataResponse.billDate', null)).value;
        //let operator = _.toLower(_.get(payLoad, 'productInfo_operator', ''));
        categoryId = typeof (categoryId) === 'string' ? VALIDATOR.toInt(categoryId) : categoryId;
        productId = typeof (productId) === 'string' ? VALIDATOR.toInt(productId) : productId;
        customerId = typeof (customerId) === 'string' ? VALIDATOR.toInt(customerId) : customerId;

         // this check is to set amount as null as operator like suntv doesn't provide amount in validation response. and with amount 0 we will not be able to send notification.
        if(_.get(self.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'EXCLUDE_AMOUNT_OPERATORS'], []) .indexOf(operator) > -1){
            amount  = null;
            this.logger.log("amount reset for operator  is ", amount, _.toLower(_.get(payLoad, 'productInfo_service', null)));
        }

        // if(self.includedServiceList.includes(service) && !self.includeCustomerIds.includes(customerId)) {
        //     return;
        // }
        if(dueDate) {
            if(paytype == 'credit card') {
                dueDate = dueDate.format('YYYY-MM-DD HH:mm:ss');
            } else {
                dueDate = dueDate.endOf('day').format('YYYY-MM-DD HH:mm:ss');
            }
        }
        let isValidityExpired = false;
        if(_.get(payLoad, 'gwParamsToPass', null)){
            isValidityExpired = _.get(payLoad, 'gwParamsToPass.isValidityExpired', null) == '1' ? true : false;
        }

        if(isValidityExpired == true){
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", `ISVALIDITYEXPIRED:${isValidityExpired}`, 'TYPE:COUNT']);
        }


        let record = {
            "operator": operator,
            "service": _.toLower(_.get(payLoad, 'productInfo_service', '')),
            "customerId": customerId,
            "rechargeNumber": rechargeNumber,
            "gateway": _.get(payLoad, 'currentGw', ''),
            "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
            "billDate": billDate ? billDate.format('YYYY-MM-DD') : null,
            "dueDate": dueDate ? dueDate : null,
            "amount": amount,
            "custInfoValues": _.get(payLoad, 'customerDataResponse', null),
            "paytype": _.toLower(_.get(payLoad, 'productInfo_paytype', '')),
            "productId": productId,
            "categoryId": categoryId,
            "validationTimeStamp": _.get(payLoad, 'timestamps_init', ''),
            "cache": null,
            "service_id": _.get(self.recent_bills_operators, [operator, 'serviceId'], 0),
            "customerMobile": _.get(payLoad, 'customerInfo_customer_mobile', null),
            "customerEmail": _.get(payLoad, 'customerInfo_customer_email', null),
            'paymentChannel': null,
            "circle": _.get(payLoad, 'productInfo_circle', ''),
            "retryCount": 0,
            "reason": null,
            "extra": null,
            "customer_type": _.get(payLoad, 'customerInfo_customer_type', null),
            "paymentDate": null,
            "validationChannel": _.get(payLoad, "customerInfo_channel_id", null),
            "validationSuccess": _.get(payLoad, "validationSuccessful", null),
            "user_data" : JSON.stringify(this.recentBillLibrary.getUserData(payLoad)),
            "extendedDataUsed" : _.get(payLoad, "useExtendedData", null),
            "noBill": _.get(payLoad, 'noBill', null),
            "billAlreadyPaid": _.get(payLoad, 'customerDataResponse.billAlreadyPaid', null),
            "limitExpired": _.get(payLoad, 'customerDataResponse.limitExpired', null),
            "skipBillCheck": false,
            "source": _.get(payLoad, 'source', null),
            "errorMessageCode": _.get(payLoad, 'errorMessageCode', null),
            "isGroupDisplayEnabled": _.get(payLoad, 'customerDataResponse.isGroupDisplayEnabled', null),
            "isAmountEditable": _.get(payLoad, 'customerDataResponse.isAmountEditable', null),
            "isValidityExpired":isValidityExpired
        };
       if (record.source && record.paytype != 'prepaid') {
           let errorResponse = "Source is only applicable when paytype is prepaid ";
           await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record, "FULL_BILL", null), errorResponse);
           return;
       }

        if(record.noBill || record.billAlreadyPaid || record.limitExpired || record.source){
            record.skipBillCheck = true;
            record.dueDate = null;
        }
        

        if (service.toLowerCase() === 'electricity' && this.lowBalancePrepaidElectricityAllowedOperators.includes(record.operator)){
            record.isValidPrepaidElectricityOperator = true;
            record.prepaidAmount = Math.abs((typeof (amount) === 'string') ? VALIDATOR.toFloat(amount) : amount);
            record.prePaidDueDate =  MOMENT().startOf('day').format('YYYY-MM-DD HH:mm:ss');
            const nfbDays = _.get(self.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'NBFD_CYCLE_BILLGEN', record.operator], 15);
            record.prePaidNextBillFetchDate = MOMENT().add(nfbDays, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss');
            self.recentBillLibrary.updateIsPrepaidFlag(record, self.getPrepaidFlagValue(payLoad));
            self.L.info(`convertKafkaPayloadToRecord, record - ${JSON.stringify(record)}`);
        }

        // if paytype is credit card
        if (self.commonLib.isCreditCardOperator(service)) {
            //RN1: MCN, RN2: Token, RN3: CIN, RN4: TIN, metadata.PAR:parId
            
            if(_.get(payLoad, 'userData_recharge_number_5', null)){
                record.customerMobile = _.get(payLoad, 'userData_recharge_number_5', null);
            }

            if(record.errorMessageCode == 1030) record.validationSuccess = true;

            let referenceId = null,
            parId = null,
            issuingBankCardVariant = null,
            skin_url = null,
            consent_valid_till = null,
            skin_source = null;

            try {
                if (_.isEmpty(_.get(payLoad, 'userData_recharge_number_3'))) {
                    if (!_.isEmpty(_.get(payLoad, 'userData_recharge_number_2')) && _.get(payLoad, 'userData_recharge_number_2').startsWith('CIN_')) {
                        referenceId = _.get(payLoad, 'userData_recharge_number_2').substring('CIN_'.length);
                    }
                } else {
                    referenceId = _.get(payLoad, 'userData_recharge_number_3');
                }
            } catch (error) {
                this.logger.error("prepareRecentBillData::issue in getting CIN", error, _.toLower(_.get(payLoad, 'productInfo_service', null)));
            }

            try {
                let metaData = _.get(payLoad, 'metaData', '');
                metaData = typeof metaData === 'string' ? JSON.parse(metaData) : metaData;
                parId = _.get(metaData, 'panUniqueReference', '');
            } catch(error) {
                this.logger.error("prepareRecentBillData::issue in getting PAR", error, _.toLower(_.get(payLoad, 'productInfo_service', null)));
            }

            try {
                let metaData = _.get(payLoad, 'metaData', '');
                metaData = typeof metaData === 'string' ? JSON.parse(metaData) : metaData;
                consent_valid_till = _.get(metaData, 'consentValidTill', null) == null ? null : MOMENT(_.get(metaData, 'consentValidTill', null)).format('YYYY-MM-DD HH:mm:ss');
                issuingBankCardVariant = _.get(metaData, 'issuingBankCardVariant', '');
                skin_url = _.get(metaData, 'skin_url', '');
                skin_source = _.get(metaData, 'skin_source', '');
            } catch(error) {
                this.logger.error("prepareRecentBillData::issue in getting Card Variant, source and skin", error, _.toLower(_.get(payLoad, 'productInfo_service', null)));
            }
            if(!consent_valid_till) {
                self.L.critical(`prepareRecentBillData::consent_valid_till is null for customerId ${record.customerId}`, 'and rechargeNumber', record.rechargeNumber);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:INVALID', 'TYPE:CONSENT_VALID_TILL', "OPERATOR:" + record.operator]);
            }

            let minimumPayableAmount = null;
            if(record.gateway == 'euronetpostpaid'){
                try {
                    let displayValues = _.get(payLoad, 'displayValues', '');
                    const minimumPayableAmountObject = displayValues.find(item => item.label === "Minimum Payable Amount");
                    if (minimumPayableAmountObject) {
                        minimumPayableAmount = minimumPayableAmountObject.value;
                    }
                    _.set(record, 'minimumPayableAmount', minimumPayableAmount);
                } catch(error) {
                    this.logger.error("Fetching minimumPayableAmount error", error, _.toLower(_.get(payLoad, 'productInfo_service', null)));
                }
            }

            try{
                bankAttributes = JSON.parse(_.get(self.config, ['CVR_DATA', productId, 'attributes'] , '{}'))
                bankName = _.toLower(_.get(bankAttributes, ['bank_code'] , ''));
                cardNetwork = _.toLower(_.get(bankAttributes, ['card_network'] , ''));
            } catch (err) {
                self.L.error("RecentBillsLibrary", "prepareRecentBillData::", "Trouble reading attributes data ", err);
            }
            const currentOutstandingAmount = parseFloat(_.get(payLoad, 'customerDataResponse.currentOutstandingAmount', 'NaN'));
            if (!isNaN(currentOutstandingAmount)){
                if (currentOutstandingAmount == 0){
                    amount = 0;
                    minimumPayableAmount = 0;
                    _.set(record, 'amount', amount);
                    _.set(record, 'minimumPayableAmount', minimumPayableAmount);
                    _.set(record, ['custInfoValues', 'currentBillAmount'], 0);
                    self.L.info("current outstanding is 0 setting amount 0 for custId " + record.customerId);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC",'STATUS:ZERO','TYPE:OUTSTANDING', "BANKNAME:" + bankName]);
                }
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC",'STATUS:VALID','TYPE:OUTSTANDING', "BANKNAME:" + bankName]);
                _.set(record, ['custInfoValues', 'current_outstanding_amount'], currentOutstandingAmount);
            }else{
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC",'STATUS:INVALID','TYPE:OUTSTANDING', "BANKNAME:" + bankName]);
            }
            _.set(record, 'currentOutstandingAmount', currentOutstandingAmount);
            _.set(record, ['custInfoValues', 'currentMinBillAmount'], minimumPayableAmount);

            if(record.rechargeNumber && record.rechargeNumber.indexOf("X") == 0) {
                _.set(record, 'newFormatMCN', true);  // starts with X instead of number
            }
            _.set(record, 'isCreditCardOperator', true);
            _.set(record, 'bankName', bankName);
            _.set(record, 'cardNetwork', cardNetwork);
            _.set(record, 'issuingBankCardVariant', issuingBankCardVariant);
            _.set(record, 'skin_url', skin_url);
            _.set(record, 'skin_source', skin_source);
            _.set(record, 'consent_valid_till', consent_valid_till);

            if(!referenceId && parId && parId != '') {
                 const TIN = _.get(payLoad, 'userData_recharge_number_4', '');
                _.set(record, 'parId', parId);
                _.set(record, 'tin', TIN);
                _.set(record, 'tokenisedCreditCard', true);
                _.set(record, 'newFormatMCN', false);
                _.set(record, 'referenceId', parId); //referenceId is part of sql table's unique constraint
            } else {
                _.set(record, 'referenceId', referenceId);
            }

        

            // if(_.get(record, 'referenceId', null) == null && _.get(record, 'gateway', null) == 'euronetpostpaid') {
            //     let recharge_number = _.get(record, 'rechargeNumber', '').replace(/\s+/g, '').slice(-4);
            //     if(bankName != '' && recharge_number && _.get(record, 'customerMobile', null)) {
            //         let reference_id = recharge_number + bankName + _.get(record, 'customerMobile', null);
            //         _.set(record, 'referenceId', reference_id);
            //     }

            // }
        }

        return record;
    }

    async validateKafkaPayload(record) {
        let self = this;
        if (!_.get(record, "validationSuccess", false) && !_.get(record, "extendedDataUsed", false) 
            && _.get(record, ['custInfoValues', 'billerDown'], false) && _.get(self, ['operatorUpAllowedCategoryOperatorMap', _.get(record, 'categoryId', '')], null)) {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:ERROR', 'TYPE:BILLER_DOWN',`CATEGORY_ID:${_.get(record, 'categoryId', null)}`]);
            let client = _.get(record, "validationChannel", "").split(" ")[0].toLowerCase();
            let allowedOperators = _.get(self, ['operatorUpAllowedCategoryOperatorMap', _.get(record, 'categoryId', '')], null);
            if ((client == "androidapp" || client == "iosapp") && (allowedOperators == null || allowedOperators.includes(_.get(record, "operator")) || allowedOperators.includes("all"))) {

                let amountAllowedCategory = _.get(self, 'operatorUpAmountAllowedCategory', []);
                if (amountAllowedCategory.includes(parseInt(_.get(record, 'categoryId', null)))) {
                    let userData = _.get(record, 'user_data', {});
                
                    if (typeof userData === 'string') {
                        try {
                            userData = JSON.parse(userData);
                        } catch (e) {
                            self.L.error('validateKafkaPayload:: unable to parse user data operator up :: error:', e);
                            userData = {};
                        }
                    }
                
                    userData.amount = _.get(record, 'amount', null);
                    _.set(record, 'user_data', JSON.stringify(userData));
                }
                
                self.droppedTransactions.getDroppedTransactionByCustAndRecharge((err, customerDetails) => {
                    if (err) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:OPERATOR_UP_NOTIFY", 'STATUS:DB_READ_AT_WRITE_FAILURE',`CATEGORY_ID:${_.get(record, 'categoryId', null)}`]);
                        self.L.error(`processRecord:: unable to fetch Record::: error:${err}`);
                    }

                    if (!customerDetails || customerDetails.length <= 0) {
                        self.droppedTransactions.storeDroppedTransaction(function (error, data) {
                            if (error) {
                                utility._sendMetricsToDD(1, ["REQUEST_TYPE:OPERATOR_UP_NOTIFY", 'STATUS:DB_INSERT_FAILURE',`CATEGORY_ID:${_.get(record, 'categoryId', null)}`]);
                            }
                        }, record);
                    }
                }, _.get(record, "customerId"), _.get(record, "rechargeNumber"));
            }
            let err="Biller is Down";
            await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record,"FULL_BILL",null),err);
            return 'Invalid record!';
        }

        if (!record || (!record.validationSuccess && !record.skipBillCheck && !record.isValidityExpired)) {
            if (record) {
                let err = 'Validation failed for pending bill';
                await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record, "FULL_BILL", null), err);
            }

            utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:ERROR', 'TYPE:VALIDATION_FAIL']);
            return 'Invalid record!';
        }

        let response = '';
        let debugKey = `rech_num:${record.rechargeNumber}::operator:${record.operator}::productId:${record.productId}::custId:${record.customerId}`;
        if(_.get(record, 'paytype', null) == 'credit card') {
            debugKey = `rech_num:${this.encryptionDecryptionHelper.encryptData(record.rechargeNumber)}::operator:${record.operator}::productId:${record.productId}::custId:${record.customerId}`;
        }
        _.set(record, 'debugKey', debugKey);
        let mandatoryParams = ['rechargeNumber', 'customerId', 'operator', 'service', 'paytype'];
        
        if(record.source) {
            mandatoryParams.push('circle')
        }
        else{
            mandatoryParams.push('productId')
        }
        if(record.paytype == 'credit card') mandatoryParams.push('referenceId'); 

        if(!record.skipBillCheck && record.gateway != 'euronetpostpaid'){
            mandatoryParams.push('dueDate');
            mandatoryParams.push('amount');
        }
        //FullBill==!record.skipBillCheck&&record.duedate&&recoord.amount

        if(_.get(self.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'EXCLUDE_AMOUNT_OPERATORS'], []) .indexOf(record.operator) > -1){
          self.L.log('validateKafkaPayload :: removing amount from mandatory key for operator',record.operator);
          mandatoryParams = mandatoryParams.filter(item => item !== 'amount');
        }
        let fieldsNotPresent = [];
        let excludedChannelIds = _.get(self.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'EXCLUDE_CHANNEL_ID'], false)

        // Disable Validation Sync service: Set to true if all records need to be flushed without any processing.
        if (self.disableValidationSync) {
            response = `Validation Sync Service is Diabled ${debugKey}`;
            await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record, "FULL_BILL", null), "Validation Sync Service is Diabled");
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:ERROR', 'TYPE:VALIDATION_SYNC_DISABLED']);
            return response;
        }

        // check is operator is migrated
        else if (_.get(self.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', record.operator, 'DISABLE_VALIDATION_SYNC'], false)) {
            response = `Validation Sync disabled for operator: ${record.operator}  ${debugKey}`;
            await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record, "FULL_BILL", null), `Validation Sync disabled for operator: ${record.operator}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:ERROR', 'TYPE:VALIDATION_SYNC_DISABLED']);

        }

        else if (excludedChannelIds && _.isArray(excludedChannelIds) && excludedChannelIds.indexOf(record.validationChannel) > -1) {
            response = `Channel_id: ${record.validationChannel} is disabled, excludedChannelIds: ${excludedChannelIds}`;
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:ERROR', 'TYPE:CHANNEL_ID_DISABLED']);
            await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record, "FULL_BILL", null), response);
        }

        if (response) return response;

        for (let field of mandatoryParams) {
            if (!record[field]) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:MANDATORY_FIELDS_ABSENT', "OPERATOR:" + record.operator, "FIELD:" + field]);
                fieldsNotPresent.push(field);
            }
        }

        let service = _.get(record, 'service', null);

        // check for mandatory fields
        if (fieldsNotPresent.length > 0) {
            response = `Mandatory fields not present:: ${debugKey} Missing params:: ${fieldsNotPresent.join(',')}`;
            await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record,"FULL_BILL",null), `Mandatory missing params:: ${fieldsNotPresent.join(',')}`);
        }
        else if (record.paytype != 'prepaid' && record.paytype != 'credit card' && !self.includedServiceList.includes(service)) {
            // check is operator is migrated

            if (!_.get(self.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', record.operator, 'ENABLE_VALIDATION_SYNC'], false)) {
                response = `Validation Sync not enabled for operator: ${record.operator}  ${debugKey}`;
                await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record, "FULL_BILL", null), `Validation Sync not enabled for operator: ${record.operator}`);
            }
            // check if payLoad is remindable or not
            else if (!self._isRemindable(record, self.recent_bills_operators)) {
                response = `_isRemindable false for ${debugKey}`;
                await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record, "FULL_BILL", null), `isRemindable is false`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:ERROR', 'TYPE:REMINDABLE']);
            }

            // check for amount > 0 && dueDate in future && cache flag should be false

            else if (!record.skipBillCheck) {
                let errMsgForRecord;

                if (record.amount <= 0) {
                    errMsgForRecord = `Invalid amount`;
                } else if (!MOMENT(record.dueDate).isValid()) {
                    errMsgForRecord = `Invalid dueDate`;
                } else if ((MOMENT(record.dueDate).diff(MOMENT()) < 0) && (!self.allowedServiceToSaveOldDueDate.includes(service))) {
                    errMsgForRecord = `DueDate is in the past`;
                }

                if (errMsgForRecord) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:ERROR', 'TYPE:NULL_AMOUNT_OR_PAST_DUEDATE']);
                    response = `Either amount < 0 || dueDate is invalid || dueDate in past ${debugKey}`;
                    await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record, "FULL_BILL", null), errMsgForRecord);
                }

                if (self.allowedServiceToSaveOldDueDate.includes(service) && MOMENT(record.dueDate).diff(MOMENT()) < 0) {
                    _.set(record, 'status', _.get(this.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5))
                }

            }

        }
        
        
        return response;
    }

    async compareAndUpdateDbData(record, recordsFound) {
        let self = this;
        try {
            if (record.maxPaymentDate && MOMENT(record.maxPaymentDate) > MOMENT(record.validationTimeStamp)) {
                let errorResponse = "payment already done";
                await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record,"FULL_BILL",recordsFound?"RU":"NON_RU"), errorResponse);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:ERROR', 'TYPE:COMPARE_AND_UPDATE_DATA', 'REASON:PAYMENT_DONE']);
                self.L.log('compareAndUpdateDbData:: Payment Event already processed for: ', record.debugKey);
                return;
            }

            let billsData = self.getBillsData(record, recordsFound);
            record.billsData = billsData;

            //Condition to create record is: customerId Present && no record found for same custId
            if (record.customerId && record.recordFoundOfSameCustId != "undefined" && !record.recordFoundOfSameCustId) {
                _.set(record, 'billUpdatedForSameCid', true);
                self.L.log(`compareAndUpdateDbData`, `Going to createBill records for ${record.debugKey}`)
                await self.createBill(record);
            }

            if (recordsFound) {
                self.L.log(`compareAndUpdateDbData`, `Going to updateDbRecord for ${record.debugKey}`)
                await self.updateDbRecord(record);
            }

            return;
        }
        catch (err) {
            self.L.critical("compareAndUpdateDbData:: ", err);
            return;
        }
    }

    async updateBills(params, tableName, newStatus){
        //console.log("printing the parmas :: ", params);
        let self = this;
        try{
            let
            notInUseStatus = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE', 13),
            disabledStatus = _.get(self.config, 'COMMON.bills_status.DISABLED', 7),
            paymentDone    = _.get(self.config, 'COMMON.bills_status.PAYMENT_DONE',11),
            skipUpdateOnStatus = [
                notInUseStatus, disabledStatus,paymentDone
            ],
            newAmount = 0,
            values = [newAmount];
        return new Promise((resolve,reject) => {
            if (!params.service) 
                params.service = _.get(self.config, ['CVR_DATA', params.productId, 'service'], '');
            let query = `UPDATE ${tableName} SET  amount = ?`;
            if(newStatus){
                query += `, status = ?`;
                values.push(newStatus);
            }
            query += ` WHERE recharge_number = ? AND operator = ? AND service = ? AND status not in (${skipUpdateOnStatus});`;

            values.push(params.rechargeNumber);
            values.push(_.toLower(params.operator));
            values.push(_.toLower(params.service));

            self.L.log('updateBills :: ',self.dbInstance.format(query,values));

            var latencyStart = new Date().getTime();
            self.dbInstance.exec(async function (err, data) {
                    utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'updateBills'});
                    if (err || !(data)) {
                        if(err){
                             await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(params,"FULL_BILL", "RU"), err);
                        }
                        if(!(data)){
                            await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(params,"FULL_BILL", "RU"), 'No data found in db');
                        }
                        
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:updateBills`]);
                        self.L.critical('createBill::', 'error occurred while getting data from DB: ', err);
                        return reject(err);
                    }
                    else{
                        return resolve();
                    }
            }, 'DIGITAL_REMINDER_MASTER', query, values);
        })
    }catch(e){
        self.L.error('updateBills::', e);
        return Promise.reject(e);
    }
    }

    checkForInvalidDateForCC(billsData) {
        let self = this;
        // check If next_bill_fetch_date is giving Invalid date , set it to newDate
        if(_.get(billsData, 'nextBillFetchDate', null) === 'Invalid date') {
            let newDate = MOMENT().add(2, 'days').format('YYYY-MM-DD HH:mm:ss');
            _.set(billsData, 'nextBillFetchDate', newDate);

            self.L.log('checkForInvalidDateForCC:: nextBillFetchDate is Invalid date, setting it to new date ' + _.get(billsData, 'nextBillFetchDate','') + 'for the debug key' + _.get(billsData,'debugKey',''));
        }
        // check IfbillFetchDate is giving Invalid date , set it to current date
        if(_.get(billsData, 'billFetchDate', null) === 'Invalid date') {
            _.set(billsData, 'billFetchDate', MOMENT().format('YYYY-MM-DD HH:mm:ss'));
        }
    }

    async createBill(record) {
        return new Promise(async (resolve, reject) => {
            let self = this;
            if (record.remindable) {
                let errorResponse = "remindable flag is true";
                await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record, "FULL_BILL", "RU"), errorResponse);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:SKIP', 'TYPE:CREATE_BILL', 'REASON:REMINDABLE']);
                self.L.log('createBill:: Skiping createBill because Remindable flag true for ' + record.debugKey);
                return resolve();
            }
            if (record.validationChannel == 'BOU 1') {
                let errorResponse = "channel is BOU_1";
                await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record, "FULL_BILL", "RU"), errorResponse);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:SKIP', 'TYPE:CREATE_BILL', 'REASON:BOU_1']);
                self.L.log('createBill:: Skipping createBill because of  BOU 1 channelId for ' + record.debugKey);
                return resolve();
            }
            
            if(record.paytype == 'credit card'){
                this.checkForInvalidDateForCC(record.billsData);
                self.bills.createCCBillOfValidationSync(createBillCbfucntion, record.tableName, record.billsData, false);
            }else{
                self.bills.createBillOfValidationSync(createBillCbfucntion, record.tableName, record.billsData, false);
            }

           async  function createBillCbfucntion(err) {
                if (err) {
                    await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record, "FULL_BILL", "RU"), err);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:ERROR', 'PAYTYPE:' + _.get(record.billsData, 'paytype',''), 'TYPE:DB_QUERY', "OPERATOR:" + record.operator]);
                    return reject(err);
                }
                self.L.log('createBill:: Bill created for ', record.debugKey);
                if(record.validationChannel == 'SUBS 1') {
                    record.skipNotification = false;
                    record.sendAllNotifications = true;
                }
                return resolve();
            }
        });
    }

    async updateAmountinCCBill(record, dbRecord) {
        return new Promise((resolve, reject) => {
            let self = this;
            // if(_.get(record, 'gateway', null) == 'euronetpostpaid') {
            //     return resolve();
            // }
            if (_.get(dbRecord, 'extra', null)) {
            try {
                let dbExtra = JSON.parse(_.get(dbRecord, 'extra', null));
                dbExtra.updated_data_source = "validationSync";
                //updating card variant, source and skin
                let customer_id = _.get(record, 'customerId', null);
                let user_data = JSON.parse(_.get(record, 'user_data', null));
                let cin = _.get(user_data, 'recharge_number_3', null);
                let parId = _.get(record, 'parId', null);
                if(_.get(dbRecord, 'customer_id', null) == customer_id && (_.get(dbRecord, 'reference_id', null) == cin || _.get(dbRecord, 'reference_id', null) == parId)){
                    let cardVariant =  _.get(record, 'issuingBankCardVariant', '');
                    let skin = _.get(record, 'skin_url', '');
                    let source = _.get(record, 'skin_source', '');
                    if(cardVariant){
                    _.set(dbExtra, 'issuingBankCardVariant', cardVariant);
                    }
                    if(skin){
                        _.set(dbExtra, 'skin_url', skin);
                    }
                    if(source){
                        _.set(dbExtra, 'skin_source', source);
                    }
                    
                }
                if(_.get(dbRecord, 'due_date',null)){
                    dbExtra.source_subtype_2 = "FULL_BILL";
                }else{
                    dbExtra.source_subtype_2 = "PARTIAL_BILL";
                }
                dbExtra.BBPSBillFetch = true;
                dbExtra.is_bbps = true;

                let customer_other_info_new = _.get(record, 'custInfoValues', null);

                if (customer_other_info_new && typeof customer_other_info_new === 'object') {
                    for (let key in customer_other_info_new) {
                        if (typeof customer_other_info_new[key] === 'string') {
                            customer_other_info_new[key] = customer_other_info_new[key].replace(/[?']/g, "");
                        }
                    }
                    //delete customer_other_info_new.currentMinBillAmount;
                }

                if (!isNaN(record.currentOutstandingAmount)){
                    customer_other_info_new.current_outstanding_amount = record.currentOutstandingAmount;
                    if (customer_other_info_new.current_outstanding_amount == 0){
                        self.L.info("updateCCBill:: current outstanding is 0 setting amount 0 for custId " + record.customerId);
                        _.set(customer_other_info_new, 'currentMinBillAmount', 0);
                        _.set(customer_other_info_new, 'currentBillAmount', 0);
                        dbRecord.amount = 0;
                    }
                }

                delete customer_other_info_new.currentOutstandingAmount;

                if(_.get(record, 'gateway', null) == 'euronetpostpaid'){
                    let newStatus = dbRecord.status;
                    let user_data_in = record.user_data;
                    let nbfd_new = MOMENT().format('YYYY-MM-DD HH:mm:ss');
                    let bbps_amount = null;
                    let bbps_due_date ;
                    let bbps_bill_date = MOMENT(_.get(record, 'billDate', null)).format('YYYY-MM-DD HH:mm:ss');

                    var NBFD_DAYS_TO_ADD_CC = _.get(self.config, ['SUBSCRIBER_CONFIG', 'NEXT_BILL_FETCH_DATES', record.operator], 30);
                    var daysToBeAddedWhenNBFDIsLessThanCurrentDay = _.get(self.config, ['SUBSCRIBER_CONFIG', 'NEXT_BILL_FETCH_DATES', 'NBFD_LESS_THAN_CURRENT_DAY'], 7);
                    var daysToBeAddedInNoBillDueCase = _.get(self.config, ['DYNAMIC_CONFIG', 'CC_PUBLISHER_CONFIG', 'NBFD_BASED_ON_ERRORMSG', 'days'], 7);
                    
                    if((_.get(dbRecord, 'due_date',null)!= null && MOMENT(record.dueDate , 'YYYY-MM-DD').startOf('day').diff(MOMENT(_.get(dbRecord, 'due_date',null) , 'YYYY-MM-DD').startOf('day')) == 0) 
                    || _.get(record, 'errorMessageCode', null) == 1030){
                        user_data_in = record.user_data;
                        nbfd_new = MOMENT(_.get(record, 'billDate', null)).add(NBFD_DAYS_TO_ADD_CC, 'days').format('YYYY-MM-DD HH:mm:ss');
                        if(MOMENT(_.get(record, 'nextBillFetchDate', null))<MOMENT() && _.get(record, 'billFetchDate', null) != null) {
                            nbfd_new = MOMENT(_.get(record, 'billFetchDate', null)).add(NBFD_DAYS_TO_ADD_CC, 'days').format('YYYY-MM-DD HH:mm:ss');
                            
                        }
                        if(_.get(record, 'errorMessageCode', null) == 1030){
                            if(bbps_bill_date == 'Invalid date') {
                                bbps_bill_date = MOMENT().format('YYYY-MM-DD HH:mm:ss');
                                nbfd_new = MOMENT(bbps_bill_date).add(daysToBeAddedInNoBillDueCase, 'days').format('YYYY-MM-DD HH:mm:ss');
                            }
                        }

                    }else if((_.get(dbRecord, 'due_date', null) == null) || (_.get(dbRecord, 'due_date',null)!= null && MOMENT(_.get(dbRecord, 'due_date',null)).diff(MOMENT()) < 0)){
                        if(record.dueDate!= null && MOMENT(record.dueDate).diff(MOMENT()) > 0){
                            user_data_in = record.user_data;
                            nbfd_new = MOMENT(_.get(record, 'billDate', null)).add(NBFD_DAYS_TO_ADD_CC, 'days').format('YYYY-MM-DD HH:mm:ss');
                            if(MOMENT(_.get(record, 'nextBillFetchDate', null))<MOMENT() && _.get(record, 'billFetchDate', null) != null) {
                                nbfd_new = MOMENT(_.get(record, 'billFetchDate', null)).add(NBFD_DAYS_TO_ADD_CC, 'days').format('YYYY-MM-DD HH:mm:ss');
                                
                            }
                            bbps_amount = record.amount;
                            bbps_due_date = record.dueDate;
                            newStatus = 4;
                            if(customer_other_info_new != null) {
                                customer_other_info_new.currentMinBillAmount = record.minimumPayableAmount;
                            }  
                            

                        }else if(record.dueDate!= null && MOMENT(record.dueDate).diff(MOMENT()) < 0){
                            user_data_in = record.user_data;
                            nbfd_new = MOMENT(_.get(record, 'billDate', null)).add(NBFD_DAYS_TO_ADD_CC, 'days').format('YYYY-MM-DD HH:mm:ss');
                            if(MOMENT(_.get(record, 'nextBillFetchDate', null))<MOMENT() && _.get(record, 'billFetchDate', null) != null) {
                                nbfd_new = MOMENT(_.get(record, 'billFetchDate', null)).add(NBFD_DAYS_TO_ADD_CC, 'days').format('YYYY-MM-DD HH:mm:ss');
                                
                            }
                            

                        }

                    }else if (_.get(dbRecord, 'due_date',null)!= null && MOMENT(_.get(dbRecord, 'due_date',null)).diff(MOMENT()) > 0){
                        if(record.dueDate!= null && (MOMENT(record.dueDate).diff(MOMENT()) > 0 && MOMENT(record.dueDate).diff(MOMENT(_.get(dbRecord, 'due_date',null) , 'YYYY-MM-DD').startOf('day')) <= 0)
                        || MOMENT(record.dueDate).diff(MOMENT()) < 0){
                            user_data_in = record.user_data;
                            bbps_bill_date = MOMENT(_.get(dbRecord, 'bill_date', null)).format('YYYY-MM-DD HH:mm:ss');
                            nbfd_new = MOMENT(_.get(dbRecord, 'bill_date', null)).add(NBFD_DAYS_TO_ADD_CC, 'days').format('YYYY-MM-DD HH:mm:ss');
                            if(MOMENT(_.get(record, 'nextBillFetchDate', null))<MOMENT() && _.get(record, 'billFetchDate', null) != null) {
                                nbfd_new = MOMENT(_.get(record, 'billFetchDate', null)).add(NBFD_DAYS_TO_ADD_CC, 'days').format('YYYY-MM-DD HH:mm:ss');
                                
                            }
                            

                        }else if(record.dueDate!= null && MOMENT(record.dueDate).diff(MOMENT()) > 0 && MOMENT(record.dueDate).diff(MOMENT(_.get(dbRecord, 'due_date',null) , 'YYYY-MM-DD').startOf('day')) > 0){
                            user_data_in = record.user_data;
                            nbfd_new = MOMENT(_.get(record, 'billDate', null)).add(NBFD_DAYS_TO_ADD_CC, 'days').format('YYYY-MM-DD HH:mm:ss');
                            if(MOMENT(_.get(record, 'nextBillFetchDate', null))<MOMENT() && _.get(record, 'billFetchDate', null) != null) {
                                nbfd_new = MOMENT(_.get(record, 'billFetchDate', null)).add(NBFD_DAYS_TO_ADD_CC, 'days').format('YYYY-MM-DD HH:mm:ss');
                                
                            }
                            bbps_amount = record.amount;
                            bbps_due_date = record.dueDate;
                            newStatus = 4;
                            if(customer_other_info_new != null) {
                                customer_other_info_new.currentMinBillAmount = record.minimumPayableAmount;
                            }
                            

                        }

                    }else if (_.get(dbRecord, 'due_date',null)!= null && MOMENT(_.get(dbRecord, 'due_date',null)).diff(MOMENT()) == 0){
                        user_data_in = record.user_data;
                        nbfd_new = MOMENT(_.get(record, 'billDate', null)).add(NBFD_DAYS_TO_ADD_CC, 'days').format('YYYY-MM-DD HH:mm:ss');
                        if(MOMENT(_.get(record, 'nextBillFetchDate', null))<MOMENT() && _.get(record, 'billFetchDate', null) != null) {
                            nbfd_new = MOMENT(_.get(record, 'billFetchDate', null)).add(NBFD_DAYS_TO_ADD_CC, 'days').format('YYYY-MM-DD HH:mm:ss');
                            
                        }
                        
                    }else if(_.get(dbRecord, 'due_date',null) == null && _.get(dbRecord, 'amount',null) != null){
                        user_data_in = record.user_data;
                        nbfd_new = MOMENT(_.get(record, 'billDate', null)).add(NBFD_DAYS_TO_ADD_CC, 'days').format('YYYY-MM-DD HH:mm:ss');
                        if(MOMENT(_.get(record, 'nextBillFetchDate', null))<MOMENT() && _.get(record, 'billFetchDate', null) != null) {
                            nbfd_new = MOMENT(_.get(record, 'billFetchDate', null)).add(NBFD_DAYS_TO_ADD_CC, 'days').format('YYYY-MM-DD HH:mm:ss');
                            
                        }
                        
                    }
                    if(MOMENT(nbfd_new) < MOMENT()) {
                        nbfd_new = MOMENT().add(daysToBeAddedWhenNBFDIsLessThanCurrentDay, 'days').format('YYYY-MM-DD HH:mm:ss');
                    }

                    let param = {
                            id: dbRecord.id,
                            user_data: user_data_in,
                            customerMobile: _.get(record, 'customerMobile', null),
                            nextBillFetchDate: nbfd_new,
                            amount:bbps_amount != null ? bbps_amount : dbRecord.amount,
                            dueDate : bbps_due_date != null ? bbps_due_date : _.get(dbRecord, 'due_date',null),
                            customerOtherInfo:JSON.stringify(customer_other_info_new),
                            extra : JSON.stringify(dbExtra),
                            billDate : bbps_bill_date,
                            billFetchDate : MOMENT().format('YYYY-MM-DD HH:mm:ss'),
                            status: newStatus,
                            bank_name : _.get(dbRecord , 'bank_name',_.get(dbRecord , 'bankName',null)),
                            recharge_number : _.get(record , 'rechargeNumber', _.get(record , 'recharge_number',null)),
                            customer_id: record.customerId
                        }; 
                        
                        self.bills.updateCCBBPSBillById(async function (error) {
                            if (error) {
                               await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record, "FULL_BILL", "RU"), error);
                                self.L.critical(`updateCCBillInSystem::updateCCBillByCustomerId`, `Error updating table for for ${param}_error:${error}`);
                            } 
                            else if (_.get(dbRecord, 'is_automatic', 0) != 0 && bbps_amount && bbps_due_date) {
                                let row = {
                                    "customerId": dbRecord.customer_id,
                                    "rechargeNumber": dbRecord.recharge_number,
                                    "billDate": bbps_bill_date,
                                    "amount": bbps_amount,
                                    "dueDate": bbps_due_date,
                                    "billFetchDate": param.billFetchDate,
                                    "nextBillFetchDate": param.nextBillFetchDate,
                                    "customerMobile": param.customerMobile,
                                    "customerEmail": dbRecord.customer_email,
                                    "paymentChannel": dbRecord.payment_channel,
                                    "retryCount": dbRecord.retry_count,
                                    "createdAt": dbRecord.created_at,
                                    "updatedAt": dbRecord.updated_at,
                                    "userData": param.user_data,
                                    "paymentDate": dbRecord.payment_date,
                                    "billGen": true,
                                    "source": "VALIDATION_SYNC",
                                    "machineId": OS.hostname(),
                                    "reference_id": dbRecord.reference_id,
                                  //  "product_id": dbRecord.product_id,
                                    "productId" : dbRecord.product_id,
                                    "operator": dbRecord.operator,
                                    "service": dbRecord.service
                                }
                                self.kafkaPublisher.publishData([{
                                    topic: _.get(self.config.KAFKA, 'SERVICES.AUTOMATIC_SYNC.AUTOMATIC_SYNC_TOPIC', ''),
                                    messages: JSON.stringify(row)
                                }], function (error) {
                                    if (error) {
                                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:ERROR', 'TYPE:AUTOMATIC_SYNC', "OPERATOR:" + record.operator]);
                                        self.logger.critical(`Error while publishing message in Kafka ${error} - MSG:-`, row, _.get(row, 'service', null));
                                    } else {
                                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:PUBLISHED', 'TYPE:AUTOMATIC_SYNC', "OPERATOR:" + record.operator]);
                                        self.logger.log(`Message published successfully in Kafka - MSG:-`, row, _.get(row, 'service', null));
                                    }
                                   // return cb();
                                }, [200, 800]);
                            }
                            return resolve();
                        }, record.tableName, param);
                }
                else if (dbExtra && _.get(dbExtra, 'updated_source', null) == 'validationSync' && !dbExtra.is_bbps) {
                        let param = {
                            id: dbRecord.id,
                            bill_date :_.get(record, 'billDate', null),
                            due_date : dbRecord.due_date,
                            status: dbRecord.status,
                            bill_fetch_date:  _.get(record, 'billFetchDate', null),
                            customerOtherInfo: JSON.stringify(_.get(record, 'custInfoValues', null), function (key, value) {
                                if (key && typeof (value) === 'string')
                                    return value.replace(/[?']/g, "");
                                else
                                    return value;
                            }),
                            recharge_number : _.get(record , 'rechargeNumber', _.get(record , 'recharge_number',null)),
                            bank_name : _.get(dbRecord , 'bank_name',_.get(dbRecord , 'bankName',null)),
                            extra : JSON.stringify(dbExtra),
                            amount : record.amount
                        };
                        
                        self.bills.updateCCBillByCustomerId(async function (error) {
                            if (error) {
                               await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record, "FULL_BILL", "RU"), error);
                                self.L.critical(`updateCCBillInSystem::updateCCBillByCustomerId`, `Error updating table for for ${param}_error:${error}`);
                            }
                            return resolve();
                        }, record.tableName, param); 
                }else resolve();

            } catch (error) {
                self.L.error("updateAmountinCCBill", "Error in updateCCFunction" + error);
                resolve();
            }
        }else{
            resolve();
        } 
    });
    }



    async getRecordsFromDb(record) {
        return new Promise((resolve, reject) => {
            let self = this;
            self.bills.getBillsOfSameRech(async (err, data) => {
                if (err) {
                    let errorResponse = `Error in fetching bills for same recharge number ${err}`;
                    await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record, null, null), errorResponse);
                    //save in dwh kafka and cassa
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC_GETRECORDS", 'STATUS:ERROR', "OPERATOR:" + record.operator]);
                    return reject(err);
                }
                if (!data || !_.isArray(data) || data.length < 1) return resolve(false);

                let recordOfSameCustId = data.filter((dataValue) => dataValue.customer_id == record.customerId).length > 0 ? true : false;
                let maxPaymentDate = data.reduce((prev, current) => {
                    if (!current.payment_date || !MOMENT(current.payment_date).isValid()) {
                        return prev;
                    }
                    if (!prev.payment_date || !MOMENT(prev.payment_date).isValid()) {
                        return current;
                    }
                    return MOMENT(prev.payment_date).isSameOrAfter(MOMENT(current.payment_date)) ? prev : current;
                }, {});

                let maxBillDate = data.reduce((prev, current) => {
                    if (!current.bill_date || !MOMENT(current.bill_date).isValid()) {
                        return prev;
                    }
                    if (!prev.bill_date || !MOMENT(prev.bill_date).isValid()) {
                        return current;
                    }
                    return MOMENT(prev.bill_date).isSameOrAfter(MOMENT(current.bill_date)) ? prev : current;
                }, {});

                _.set(record, 'maxPaymentDate', _.get(maxPaymentDate, 'payment_date', null));
                _.set(record, 'maxBillDate', _.get(maxBillDate, 'bill_date', null));
                _.set(record, 'dbData', self.getSortedDbData(data));
                _.set(record, 'recordFoundOfSameCustId', recordOfSameCustId);
                _.set(record, 'activeRecordsInDB', self.getActiveRecords(data));
                if(recordOfSameCustId){
                    _.set(record, 'is_automatic', data.filter((dataValue) => dataValue.customer_id == record.customerId)[0].is_automatic);
                }else{
                    _.set(record, 'is_automatic_diffCustId', data[0].is_automatic);
                }

                return resolve(true);
            }, record.tableName, record);
        });
    }

    async getRecordsFromDbByCustId(record) {
        return new Promise(async (resolve, reject) => {
            let self = this;
            try {
                self.bills.getBillByCustomer(async (err, data) => {
                    if (err) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC_GETRECORDS", 'STATUS:ERROR', "OPERATOR:" + record.operator]);
                        return reject(err);
                    }
                    if (!data || !_.isArray(data) || data.length < 1) return resolve(false);
    
                    let dbRecord = false;
                    for (let row of data) {
                        if (row.recharge_number === record.rechargeNumber && row.reference_id === record.referenceId) {
                            if(self.encryptionDecryptionHelper.isWhitelistedForCC("financial services", "credit card",record.customerId) && !this.checkIfDbRecordIsEncrypted(row)) {

                                await this.bills.deleteAllMatchingMCNs({...row});
                            } else {
                                dbRecord = row;
                            }
                        } else {
                            const last4_MCN = row.recharge_number.substr(-4);
                            if (last4_MCN == record.rechargeNumber.substr(-4)) {
                                let attributes = JSON.parse(_.get(self.config, ['CVR_DATA', row.product_id, 'attributes'], '{}'))
                                let bankName = _.toLower(_.get(attributes, ['bank_code'], ''));
                                if (record.bankName == bankName) {
    
                                    await this.bills.deleteAllMatchingMCNs({...row});
                                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:DELETE OLDER ENTRIES']);
    
                                    if(_.get(row,'is_automatic',0) == 1) {
                                        row = self.commonLib.mapBillsTableColumns(row);
                                        _.set(row, 'DELETE_AUTOMATIC', true);
    
                                        let payLoad = [{
                                            topic: _.get(self.config.KAFKA, 'SERVICES.AUTOMATIC_SYNC.AUTOMATIC_SYNC_TOPIC', ''),
                                            messages: JSON.stringify(row)
                                        }];

                                        self.kafkaPublisher.publishData(payLoad, function (error) {
                                            if (error) {
                                                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:ERROR', `TYPE:AUTOMATIC_SYNC`]);
                                                self.logger.critical(`Error while publishing message in Kafka ${error} - MSG:-`, payLoad, _.get(record, 'service', null));
                                            } else {
                                                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:PUBLISHED', `TYPE:AUTOMATIC_SYNC`]);
                                                self.logger.log(`Message published successfully in Kafka - MSG:-`, payLoad, _.get(record, 'service', null));
                                            }
                                        }, [200, 800]);
                                    }
                                }
                            }
                        }
                    }
                    return resolve(dbRecord);
                }, record.tableName, record.customerId);
            } catch (error) {
                self.L.critical('Error in getRecordsFromDbByCustId', error);
                return reject(error);
            }
        });
    }


    /**
     * Returns active users for which validation data will be updated
     * @param {*} dbRecords 
     */
    getActiveRecords(records) {
        let activeRecords = 0;
        for (let record of records) {
            if (record.status != _.get(this.config, 'COMMON.bills_status.DISABLED', 7) && record.status != _.get(this.config, 'COMMON.bills_status.NOT_IN_USE', 13)) {
                activeRecords++;
            }
        }
        return activeRecords;
    }

    /**
     * sorting DB data based on due date in descending order
     * [Use case: If some record have status=13 or 7 then code gets wrong information that we do not have latest billing cycle]
     * @param {*} data 
     */
    getSortedDbData(dbRecords) {

        dbRecords.sort(function (record1, record2) {
            let isValidR1date = MOMENT(record1.due_date).isValid(false);
            let isValidR2date = MOMENT(record2.due_date).isValid(false);

            if (isValidR1date && isValidR2date) {
                let daysDiff = MOMENT(record1.due_date).diff(record2.due_date, 'days');
                if (daysDiff > 0) return -1;
                else return 1;
            } else if (isValidR1date) {
                return -1;
            } else if (isValidR2date) {
                return 1;
            } else {
                return 0;
            }
        });

        return dbRecords;
    }

    async updateDbRecord(record) {
        return new Promise(async (resolve, reject) => {
            let self = this;
            let billsData = record.billsData,
                dbRecord = _.get(record, 'dbData', {}),
                dbDueDate = _.get(dbRecord, '[0].due_date', null),
                dueDateDiff = null,
                amount = _.get(record, 'amount', null),
                dbAmount = _.get(dbRecord, '[0].amount', null),
                maxPaymentDate = _.get(record, 'maxPaymentDate', null),
                maxBillDate = _.get(record, 'maxBillDate', null),
                service = _.get(record, 'service', null);

            if (_.get(record, 'dueDate', null) != null && dbDueDate) {
                dueDateDiff = MOMENT(record.dueDate).startOf('day').diff(MOMENT(dbDueDate).utc().startOf('day'), 'day');
            }

            // let dueDateDiff = MOMENT(record.dueDate).utc().startOf('day').diff(MOMENT(dbDueDate).utc().startOf('day'));
            let amountDiff = Math.abs(amount - dbAmount);

            if ((self.allowedPrepaidDthOperator.indexOf(_.toLower(record.operator))>-1 && MOMENT(record.dueDate) < MOMENT()) || (MOMENT(record.dueDate) < MOMENT() && self.allowedServiceToSaveOldDueDate.includes(service) && dueDateDiff === 0 && amountDiff && maxPaymentDate && maxBillDate && MOMENT(maxPaymentDate) < MOMENT(maxBillDate))) {
                _.set(billsData, 'updateForOldBill', true);
                ASYNC.parallel([
                    (cb) => {
                        self.bills.updateBillForSameRNandCID((err) => {
                            if (err) {
                                self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record, "FULL_BILL", "RU"), err, cb);
                                //cb(err);
                            }
                            else {
                                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:SUCCESS', 'TYPE:UPDATE', "OPERATOR:" + record.operator]);
                                record.skipNotification = true;
                                record.updateInRecents = true;
                                cb(null);
                            }
                        }, record.tableName, billsData);
                    },
                    (cb) => {
                        self.L.log(`updateDbRecord`, `dth, invoking updateBillForSameRNandDiffCID for ${record.debugKey}`);
                        self.bills.updateBillForSameRNandDiffCID((err) => {

                            if (err) {
                                self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record, "FULL_BILL", "RU"), err, cb);
                                //cb(err);
                            }
                            else {
                                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:SUCCESS', 'TYPE:UPDATE', "OPERATOR:" + record.operator]);
                                record.skipNotification = true;
                                record.updateInRecents = true;
                                cb(null);
                            }
                        }, record.tableName, billsData);
                    }
                ], (err) => {
                    if (err) {
                        return reject(err);
                    } else {
                        return resolve(null);
                    }
                }
                );
            } else if (MOMENT(dbDueDate).isValid() && (!dueDateDiff || dueDateDiff < 1) && (!self.includedServiceList.includes(service)) && (!self.allowedPrepaidDthOperator.includes(_.toLower(record.operator)))) {
                let error= `validation dueDate:${MOMENT(record.dueDate).startOf('day').format('YYYY-MM-DD')} <= dbDueDate:${MOMENT(dbDueDate).utc().startOf('day').format('YYYY-MM-DD')}, So skipping update for record`;
                await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record, "FULL_BILL", "RU"), error);
            // if (MOMENT(dbDueDate).isValid() && (dueDateDiff===undefined || dueDateDiff ===null || dueDateDiff < 0)) {
                self.L.log('updateRecord', `validation dueDate:${MOMENT(record.dueDate).startOf('day').format('YYYY-MM-DD')} <= dbDueDate:${MOMENT(dbDueDate).utc().startOf('day').format('YYYY-MM-DD')}, So skipping update for ${record.debugKey}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:UPDATE_SKIP', 'TYPE:OLD_DUE_DATE', "OPERATOR:" + record.operator]);
                return resolve(null);
            }
            else if(_.get(record,'dbBillSource',null) == 'UPMS' && (dueDateDiff===0 && !amountDiff)){
                self.L.log('updateRecord', `validation amount:${amount} == dbAmount:${dbAmount} for UPMS record, So skipping update for ${record.debugKey}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:UPDATE_SKIP', 'TYPE:DB_RECORD_SAME_AMOUNT_AND_DUEDATE_UPMS', "OPERATOR:" + record.operator]);
                return resolve();
            }
            else if(record.activeRecordsInDB == 0){
                let err= `No actve records in DB, so skipping update for record`;
                await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record, "FULL_BILL", "RU"), err);
                self.L.log('updateRecord', `No actve records in DB, so skipping update for ${record.debugKey}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:UPDATE_SKIP', 'TYPE:NO_ACTIVE_RECORDS', "OPERATOR:" + record.operator]);
                return resolve();
            }
            else if (MOMENT(record.dueDate).isValid() && MOMENT(record.dueDate) > MOMENT() || (this.lowBalancePrepaidElectricityAllowedOperators.includes(record.operator) && self.recentBillLibrary.checkIfIsPrepaidIsSet(record))) {
                // TODO: Handle corner case bill fetch after billDue
                ASYNC.parallel([
                    (cb) => {
                        if(_.get(record, 'billUpdatedForSameCid', false)==true){
                            cb(null);
                        }else{
                            self.L.log(`updateDbRecord`, `invoking updateBillForSameRNandCID for ${record.debugKey}`);
                            self.bills.updateBillForSameRNandCID((err) => {
                                if (err) {
                                    self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record, "FULL_BILL", "RU"), err,cb); 
                                    //cb(err);
                                }
                                else {
                                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:SUCCESS','TYPE:UPDATE', "OPERATOR:" + record.operator]);
                                    record.skipNotification = false;
                                    record.updateInRecents = true;
                                    cb(null);
                                }
                            }, record.tableName, billsData);
                        }
                    },
                    (cb) => {
                        if(!self.includedServiceList.includes(service)){
                        self.L.log(`updateDbRecord`, `invoking updateBillForSameRNandDiffCID for ${record.debugKey}`);
                            self.bills.updateBillForSameRNandDiffCID((err) => {
                                if (err) {
                                    self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record, "FULL_BILL", "RU"), err,cb); 
                                    //cb(err);
                                }
                                else {
                                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:SUCCESS','TYPE:UPDATE', "OPERATOR:" + record.operator]);
                                    record.skipNotification = false;
                                    record.updateInRecents = true;
                                    cb(null);
                                }
                            }, record.tableName, billsData);
                        } else{
                            cb(null);
                        }
                        
                    },
                    (cb) => {
                        self.bills.resetIsAutomatic(cb, record.tableName, billsData.rechargeNumber, [3,4]);
                    }
                ], (err) => {
                    if (err) {
                        return reject(err);
                    } else {
                        return resolve(null);
                    }
                }
                );
            } else {
                self.L.log(`updateDbRecord, MOMENT(record.dueDate) > MOMENT() : ${MOMENT(record.dueDate) > MOMENT()}, skipping update for ${record.debugKey}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:UPDATE_SKIP', 'TYPE:OTHER', "OPERATOR:" + record.operator]);
                return resolve();
            }
        });
    }


    getBillsData(record, recordFound = false) {
        let self = this;
        let dbRecord = _.get(record, 'dbData[0]', {}),
            extraDetails = {},
            oldBillFetchDate = null;
        if (_.get(record, 'status', null) == _.get(this.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5)) {
            if (_.get(dbRecord, 'extra', null)) {
                try {
                    extraDetails = JSON.parse(_.get(dbRecord, 'extra', null));
                } catch (error) {
                    self.L.error("getBillsData", "Error in JSON parsing" + error);
                }
                extraDetails.lastAmount = _.get(dbRecord, 'amount', null);
            }
            if (_.get(dbRecord, 'old_bill_fetch_date', null) != null) {
                oldBillFetchDate = MOMENT(_.get(dbRecord, 'old_bill_fetch_date', null)).startOf('day').format('YYYY-MM-DD HH:mm:ss');
            }
            else {
                oldBillFetchDate = MOMENT().startOf('day').format('YYYY-MM-DD HH:mm:ss');
            }
        }
        else {
        extraDetails.lastSuccessBFD = _.get(dbRecord, 'bill_fetch_date', null);
        extraDetails.billFetchDate = MOMENT(record.validationTimeStamp).isValid() ? MOMENT(record.validationTimeStamp).format('YYYY-MM-DD HH:mm:ss') : MOMENT().format('YYYY-MM-DD HH:mm:ss');
        extraDetails.lastDueDt = _.get(dbRecord, 'due_date', null);
        extraDetails.lastBillDt = _.get(dbRecord, 'bill_date', null);
        extraDetails.lastAmount = _.get(dbRecord, 'amount', null);
        }
        extraDetails.billSource = `ValidationSync-${MOMENT().format('YYYY-MM-DD HH:mm:ss')}`;
        extraDetails.customer_type = _.get(record, 'customer_type', null);
        extraDetails.updated_source = "validationSync";
        extraDetails.updated_data_source = "validationSync";
        extraDetails.created_source = "validationSync";  // fetch data from db and update creted source
        extraDetails.source_subtype_2 = 'FULL_BILL';
        if (_.get(record, 'isGroupDisplayEnabled', null) === "0") {
            extraDetails.isGroupDisplayEnabled = false;
        }
        else if (_.get(record, 'isGroupDisplayEnabled', null) === "1") {
            extraDetails.isGroupDisplayEnabled = true;
        }
        else {
            extraDetails.isGroupDisplayEnabled = null;
        }
        if (_.get(record, 'isAmountEditable', false) === "0") {
            extraDetails.isAmountEditable = false;
        }
        else if (_.get(record, 'isAmountEditable', false) === "1") {
            extraDetails.isAmountEditable = true;
        }
        else {
            extraDetails.isAmountEditable = null;
        }
        delete extraDetails.upmsRegistrationNumber;
        delete extraDetails.upmsBillPaymentToken;
        if (_.get(dbRecord, 'extra', null)) {
            try {
                let dbExtra = JSON.parse(_.get(dbRecord, 'extra', null));
                if (dbExtra) {
                    if (_.get(dbExtra, 'blockedBy', null)) extraDetails.blockedBy = _.get(dbExtra, 'blockedBy', null);
                    if (_.get(dbExtra, 'created_source', null)) extraDetails.created_source = _.get(dbExtra, 'created_source', null);
                    if(_.get(dbExtra, 'errorCounters', null)) extraDetails.errorCounters = {};
                }
            }catch(error)
            {
                self.L.error("getBillsData", "Error in JSON parsing" + error);
            }
        }

        let billsData = {
            user_data: record.user_data,
            nextBillFetchDate: record.nextBillFetchDate,
            billFetchDate: extraDetails.billFetchDate,
            commonAmount: record.amount,
            commonDueDate: record.dueDate ? MOMENT(record.dueDate).format('YYYY-MM-DD'): null,
            rechargeNumber: record.rechargeNumber,
            billDate: record.billDate,
            productId: record.productId,
            commonStatus: _.get(record, 'status', 4),
            customerId: record.customerId,
            customerMobile: _.get(record, 'customerMobile', null),
            operator: record.operator,
            circle: record.circle,
            service: record.service,
            gateway: record.gateway,
            retryCount: 0,
            reason: null,
            paytype: _.get(record, 'paytype', null),
            customerEmail: _.get(record, 'customerEmail', null),
            service_id: _.get(record, 'service_id', 0),
            is_automatic: _.get(record, 'is_automatic', null),
            is_automatic_diffCustId : _.get(record, 'is_automatic_diffCustId', null),
            resetRemindLaterDate: _.get(record, 'resetRemindLaterDate', false),
            extra: JSON.stringify(extraDetails, function (key, value) {
                if (key && typeof (value) === 'string')
                    return value.replace(/[?']/g, "");
                else
                    return value;
            }),
            customerOtherInfo: JSON.stringify(record.custInfoValues, function (key, value) {
                if (key && typeof (value) === 'string')
                    return value.replace(/[?']/g, "");
                else
                    return value;
            }),
            oldBillFetchDate: oldBillFetchDate
        };

        try{
            let highestPriorityAmongestRows = self.billsLib.getHighestPriorityAmongestRows(_.get(record, 'dbData',[]));
            let highestPublishedDateAmongestRows = self.billsLib.getHighestPublishedDateAmongestRows(_.get(record, 'dbData',[]));
            billsData = self.billsLib.updateRecordWithOffsetNbfd(billsData, highestPriorityAmongestRows, highestPublishedDateAmongestRows);
        }catch(e){
            self.L.error("getBillsData", "Error in updating record with offset nbfd" + e);
        }

        return billsData;
    }

    //updated_data_source 
    //source_subtype_2
    //user_type
    //recon_id

    getCCBillsData(record) {
        let self = this;
   
        let extraDetails = {};
        extraDetails.updated_source = 'validationSync';
        extraDetails.updated_data_source = 'validationSync';
        extraDetails.created_source = 'validationSync';
        extraDetails.source_subtype_2 = 'PARTIAL_BILL';
        if(_.get(record, 'gateway', null) == 'euronetpostpaid') {
            extraDetails.is_bbps = true;
            delete record.custInfoValues.currentOutstandingAmount;
        }

               
        let billsData = {
            user_data: record.user_data,
            billFetchDate: MOMENT().format('YYYY-MM-DD'),
            bankName: record.bankName,
            cardNetwork: record.cardNetwork,
            parId: _.get(record, 'parId', null),
            tin: _.get(record, 'tin', null),
            referenceId: record.referenceId,
            commonAmount: record.amount,
            rechargeNumber: record.rechargeNumber,
            billDate: record.billDate,
            productId: record.productId,
            commonStatus: 4,
            customerId: record.customerId,
            customerMobile: _.get(record, 'customerMobile', null),
            operator: record.operator,
            circle: record.circle,
            service: record.service,
            gateway: record.gateway,
            retryCount: 0,
            reason: null,
            paytype: _.get(record, 'paytype', null),
            customerEmail: _.get(record, 'customerEmail', null),
            service_id: _.get(record, 'service_id', 0),
            extra: JSON.stringify(extraDetails),
            customerOtherInfo: JSON.stringify(record.custInfoValues, function (key, value) {
                if (key && typeof (value) === 'string')
                    return value.replace(/[?']/g, "");
                else
                    return value;
            }),
            debugKey: _.get(record, 'debugKey', null),
        };
        if(_.get(billsData, 'gateway', null) == 'euronetpostpaid') {
            _.set(billsData, 'billFetchDate', MOMENT(_.get(record, 'billFetchDate', null)).format('YYYY-MM-DD'));
            _.set(billsData, 'dueDate', record.dueDate);
            _.set(billsData, 'nextBillFetchDate', record.nextBillFetchDate);
        }
        if(_.get(record, 'paytype', null) == 'credit card'){
            _.set(billsData, 'issuingBankCardVariant', _.get(record, 'issuingBankCardVariant', null));
            _.set(billsData, 'skin_url', _.get(record, 'skin_url', null));
            _.set(billsData, 'skin_source', _.get(record, 'skin_source', null));
            _.set(billsData,'consent_valid_till', _.get(record, 'consent_valid_till', null));
        }

        return billsData;
    }

    getNextBillFetchDate(record, currentAutomaticStatus=null) {
        let self = this;
        let service = _.get(record, 'service', null);
        if(self.includedServiceList.includes(service)) {
            return MOMENT().add(5, 'days').format('YYYY-MM-DD HH:mm:ss');
        }
        let billDateBasedGateways = _.get(self.config, 'SUBSCRIBER_CONFIG.BILL_FETCH_BASED_ON_BILL_DATE_GATEWAYS', []);
        let dateToBeUsed = billDateBasedGateways.indexOf(record.operator) > -1 && record.billDate ? record.billDate : record.dueDate;

        if (billDateBasedGateways.indexOf(record.operator) > -1 && !record.billDate) {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:BILLDATE_ABSENT_BILLDATEBASEDGATEWAY', "OPERATOR:" + record.operator]);
        }

        let nextBillFetchDate = self.billSubscriber.getFirstBillFetchInterval(record.operator, currentAutomaticStatus) < 0 ? MOMENT(dateToBeUsed).add(Math.abs(Number(self.billSubscriber.getFirstBillFetchInterval(record.operator, currentAutomaticStatus))), 'months') : MOMENT(dateToBeUsed).add(self.billSubscriber.getFirstBillFetchInterval(record.operator, currentAutomaticStatus), 'days');

        if((record.paytype == 'prepaid' && record.service =='dth' && self.allowedPrepaidDthOperator.indexOf(_.toLower(record.operator))>-1)){
            nextBillFetchDate =  MOMENT(dateToBeUsed).add(self.billSubscriber.getFirstBillFetchInterval(record.operator, currentAutomaticStatus), 'days');
        }
        if (nextBillFetchDate < MOMENT()) {
            nextBillFetchDate = MOMENT().add(1, 'days');
            //Do I have to do anything here
            self.L.error(`getNextBillFetchDate:: NBFD is setting in past debugKey: ${record.debugKey} NBFD: ${nextBillFetchDate}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:NBFD_SETTING_IN_PAST', "OPERATOR:" + record.operator]);
        }
        return nextBillFetchDate.format('YYYY-MM-DD HH:mm:ss');
    }



    _isRemindable(record, recent_bills_operators) {
        let self = this;
        let remindable_operator = _.get(recent_bills_operators, record.operator, null);
        let paytypes = [];

        if (remindable_operator) {
            paytypes = _.get(remindable_operator, 'paytypes', ['postpaid']);
            return paytypes.indexOf(record.paytype) < 0 ? false : true;
        } else {
            let supported_paytype = ['postpaid', 'fee payment', 'insurance', 'loan', 'credit card'];
            return supported_paytype.indexOf(record.paytype) < 0 ? false : true;
        }
    }


    /**
     * In case we want to publish the current record too, we create a record in following format
     * @param {record} record 
     * @returns 
     */
    getNewRecordForPublishing(record){
        let newRecord = {
            customerId: record.customerId,
            rechargeNumber: record.rechargeNumber,
            productId: record.productId,
            operator: record.operator,
            amount: record.amount,
            billDate: record.billDate,
            dueDate: record.dueDate,
            billFetchDate: record.billFetchDate,
            nextBillFetchDate: record.nextBillFetchDate,
            gateway: record.gateway,
            paytype: record.paytype,
            service: record.service,
            circle: record.circle,
            customerMobile: record.customerMobile,
            customerEmail: record.customerEmail,
            paymentChannel: record.paymentChannel,
            retryCount: record.retryCount,
            status: record.status,
            reason: record.reason,
            extra: record.extra,
            published_date: record.published_date,
            createdAt: record.createdAt,
            updatedAt: record.updatedAt,
            userData: record.user_data,
            notification_status: record.notification_status,
            paymentDate: record.paymentDate,
            service_id: record.service_id,
            customerOtherInfo: record.customerOtherInfo,
            is_automatic: record.is_automatic,
            dl_last_updated: record.dl_last_updated,
            billGen: record.billGen,
            source: record.source,
            machineId: record.machineId
        };
        return newRecord
    }

    async updateBillsInRecent(record) {
        let self = this;
        let queryParam = {
            recharge_number: _.get(record, 'rechargeNumber', null),
            operator: _.get(record, 'operator', null),
            paytype: _.get(record, 'paytype', null),
            service: _.get(record, 'service', null),
            validationBillSync: true,
            validation_req_time: MOMENT(_.get(record, 'validationTimeStamp', null)).isValid() ? MOMENT(record.validationTimeStamp).format('YYYY-MM-DD HH:mm:ss') : null,
        }, fieldValue = {
            due_date: record.dueDate,
            bill_date: record.billDate,
            amount: _.get(record, 'amount', null),
            original_due_amount: _.get(record, 'amount', null),
            label: _.get(record, 'amount', null) && _.get(record, 'dueDate', null) ? `Bill Payment of Rs${_.get(record, 'amount', null)} due on ${MOMENT(record.dueDate).format('DD MMM YYYY')}` : null
        };
        return new Promise((resolve, reject) => {
            self.recentsLayerLib.update(function (error) {
                if(error){
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:ERROR', 'TYPE:RECENT_UPDATE']);
                }
                self.L.log('updateBillsInRecent::recentsLayerLib.update', `update recents request completed for ${record.debugKey},error if any is:${error}`);
                resolve(null);
            }, queryParam, "bills", [fieldValue], "validationSync");
        })
    }

    publishInKafka(record) {
        return new Promise((resolve, reject) => {
            let self = this;
            let skipBillFetchPublish = false;
            let dbData = _.get(record, 'dbData', []);
            // In case of a new record, we publish only for SUBS 1
            if(record.sendAllNotifications && record.sendAllNotifications == true) {
                let newRecord = self.getNewRecordForPublishing(record);
                dbData.push((newRecord))
            }

            if (!record.service || !record.operator || !record.rechargeNumber || !record.productId) {
                self.L.critical('publishInKafka :: invalid inputs ', record.service, record.operator, record.rechargeNumber, record.productId);
                return resolve();
            }

            let publishedToAutomatic = false;
            ASYNC.eachLimit(dbData, 1, (dataRow, cb) => {
                let dbDebugKey = `rech:${dataRow.recharge_number}::cust:${dataRow.customer_id}::op:${dataRow.operator}`;
                if ((record.validationChannel != 'SUBS 1') && (!record.customerId || dataRow.customer_id == record.customerId || dataRow.status == 13 || dataRow.status == 7 || record.status != 4)) {
                    self.L.log('publishInKafka', `Skipping sending Notfication for ${record.debugKey}, dbRow::${dbDebugKey}`);
                    skipBillFetchPublish = true;
                }

                let billsData = record.billsData;

                let dbDueDate = dataRow.due_date;
                dataRow.due_date = MOMENT(record.dueDate, 'YYYY-MM-DD HH:mm:ss').startOf('day').format('YYYY-MM-DD HH:mm:ss');
                if (record.is_automatic && [1, 2, 3, 4, 5, 8].indexOf(record.is_automatic) != -1) {
                    dataRow.is_automatic = record.is_automatic;
                }
                else if (record.is_automatic_diffCustId && [1, 2, 3, 4, 5, 8].indexOf(record.is_automatic_diffCustId) != -1) {
                    dataRow.is_automatic = record.is_automatic_diffCustId;
                }
                dataRow.bill_date = record.billDate ? MOMENT(record.billDate, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD') : null;
                dataRow.amount = record.amount;
                dataRow.bill_fetch_date = _.get(billsData, 'billFetchDate', null);
                dataRow.next_bill_fetch_date = _.get(billsData, 'nextBillFetchDate', null);
                dataRow.status = _.get(billsData, 'commonStatus', 0);
                dataRow.extra = _.get(billsData, 'extra', null);
                dataRow.updated_at = MOMENT().format('YYYY-MM-DD HH:mm:ss');
                dataRow.customerOtherInfo = _.get(billsData, 'customerOtherInfo', null);

                let row = self.commonLib.mapBillsTableColumns(dataRow);
                row.billGen = true;
                row.source = "VALIDATION_SYNC";
                row.machineId = OS.hostname(); 
                if (_.get(row, 'is_automatic', 0) != 0 && _.get(row, 'is_automatic', 0) != 5 && _.get(row, 'is_automatic', 0) != 8) {
                    if(!publishedToAutomatic){
                        publishedToAutomatic = true;
                        self.kafkaPublisher.publishData([{
                            topic: _.get(self.config.KAFKA, 'SERVICES.AUTOMATIC_SYNC.AUTOMATIC_SYNC_TOPIC', ''),
                            messages: JSON.stringify(row),
                            key: _.get(row, 'rechargeNumber','')
                        }], function (error) {
                            if (error) {
                                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:ERROR', 'TYPE:AUTOMATIC_SYNC', "OPERATOR:" + record.operator]);
                                self.L.critical('Error while publishing message in Kafka - MSG:- ' + JSON.stringify(row), error);
                            } else {
                                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:PUBLISHED', 'TYPE:AUTOMATIC_SYNC', "OPERATOR:" + record.operator]);
                                self.L.log('Message published successfully in Kafka', ' on topic AUTOMATIC_SYNC', JSON.stringify(row));
                            }
                            return cb();
                        }, [200, 800]);
                    }else{
                        return cb();
                    }
                }
                else if (MOMENT(record.dueDate).diff(MOMENT(dbDueDate), 'days') > 0 && skipBillFetchPublish == false) {
                    row.skipNotification = self.greyScaleEnv ? 1 : 0;
                    let payload = {
                        source: "validationSync",
                        notificationType: "BILLGEN",
                        data: row
                    };
                    let toBeNotifiedRealtime = self.commonLib.decideTopicToPublishBillGen();
                    if(!toBeNotifiedRealtime){
                        utility.sendNotificationMetricsFromSource(payload)
                        self.kafkaBillFetchPublisher.publishData([{
                            topic: _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH', ''),
                            messages: JSON.stringify(payload)
                        }], function (error) {
                            if (error) {
                                utility.sendNotificationMetricsFromSource(payload,"ERROR")
                                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:ERROR','TYPE:REMINDER_BILL_FETCH', "OPERATOR:" + record.operator]);
                                self.L.critical('publishInKafka :: kafkaBillFetchPublisher', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
                            } else {
                                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:PUBLISHED','TYPE:REMINDER_BILL_FETCH', "OPERATOR:" + record.operator]);
                                self.L.log('prepareKafkaResponse :: kafkaBillFetchPublisher', 'Message published successfully in Kafka', ' on topic REMINDER_BILL_FETCH', JSON.stringify(payload));
                            }
                            return cb();
                        }, [200, 800]);
                    }
                    else{
                        payload.source = 'BillGenValidationSyncRealTime';
                        utility.sendNotificationMetricsFromSource(payload)
                        self.billFetchKafkaRealtime.publishData([{
                            topic: _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE_REALTIME.REMINDER_BILL_FETCH_REALTIME', ''),
                            messages: JSON.stringify(payload)
                        }], function (error) {
                            if (error) {
                                utility.sendNotificationMetricsFromSource(payload,"ERROR")
                                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:ERROR','TYPE:REMINDER_BILL_FETCH', "OPERATOR:" + record.operator]);
                                self.L.critical('publishInKafka :: kafkaBillFetchPublisher', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
                            } else {
                                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:PUBLISHED','TYPE:REMINDER_BILL_FETCH', "OPERATOR:" + record.operator]);
                                self.L.log('prepareKafkaResponse :: kafkaBillFetchPublisherRealTime', 'Message published successfully in Kafka', ' on topic REMINDER_BILL_FETCH', JSON.stringify(payload));
                            }
                            return cb();
                        }, [200, 800]); 
                    }
                } else {
                    return cb();
                }

            }, function () {
                return resolve();
            });
        });

    }

    async publishCtEvents(record) {

        let dbRecord = _.get(record, 'dbData', {});
        let dbDueDate = _.get(dbRecord, '[0].due_date', null);
        // let amount = _.get(record, 'amount', null);
        // let dbAmount = _.get(dbRecord, '[0].amount', null);
        let dueDateDiff = MOMENT(record.dueDate).startOf('day').diff(MOMENT(dbDueDate).utc().startOf('day'));
        // let amountDiff = Math.abs(amount - dbAmount);

        // all conditions taken from update and create functions
        if (MOMENT(dbDueDate).isValid() && (!dueDateDiff || dueDateDiff < 1)) {
        // if (MOMENT(dbDueDate).isValid() && ( dueDateDiff===undefined || dueDateDiff===null || dueDateDiff < 0)) {
            return;
        }
        // if(!amount || (dueDateDiff===0 && !amountDiff)){
        //     return;
        // }
        if(record.activeRecordsInDB == 0){
            return;
        }
        if (record.maxPaymentDate && MOMENT(record.maxPaymentDate) > MOMENT(record.validationTimeStamp)) {
            return;
        }
        if (record.remindable) {
            return;
        }
        if (record.validationChannel == 'BOU 1') {
            return;
        }

        let self = this;
        let dbData = _.get(record, 'dbData', []);
        let custIdExists = _.get(record, 'recordFoundOfSameCustId', false)
        const productId = _.get(record, 'productId', 0);

        let newRecord = self.getNewRecordForPublishing(record);
        if(!custIdExists)
            dbData.push(newRecord);

        ASYNC.eachLimit(dbData, 3, (dataRow, cb) => {
            let eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'BILLGEN'], 'reminderBillGen')
            if(!dataRow.customer_id && dataRow.customerId){ // only when we have a completely new recharge number
                dataRow.customer_id = dataRow.customerId
                dataRow.recharge_number = dataRow.rechargeNumber
            }
            let dbDebugKey = `rech:${dataRow.recharge_number}::cust:${dataRow.customer_id}::op:${dataRow.operator}`;
            let billsData = record.billsData;

            if(dataRow.customer_id === record.customerId){
                eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'VALIDATION_SYNC'], 'validationBillGen')
            }

            dataRow.due_date = MOMENT(record.dueDate, 'YYYY-MM-DD HH:mm:ss').startOf('day').format('YYYY-MM-DD HH:mm:ss');
            if (record.is_automatic && (record.is_automatic == 1 || record.is_automatic == 3)) {
                dataRow.is_automatic = 1;
            }
            if(record.is_automatic && record.is_automatic == 5 || record.is_automatic == 8) {
                dataRow.is_automatic = record.is_automatic;
            } else {
                dataRow.is_automatic = 0
            }

            if(dataRow.notification_status == 0){
                self.L.error(`stop publishing data on CleverTap via the Kafka pipeline for notification status : ${dataRow.notification_status} debugKey::`, dbDebugKey);
                return cb();                         
            }
            
            if(dataRow.notification_status == null)
                dataRow.notification_status = 1
            dataRow.bill_date = record.billDate ? MOMENT.utc(record.billDate, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD') : null;
            dataRow.amount = record.amount;
            dataRow.bill_fetch_date = MOMENT.utc(_.get(billsData, 'billFetchDate', null)).format('YYYY-MM-DD HH:mm:ss');
            dataRow.next_bill_fetch_date = MOMENT.utc(_.get(billsData, 'nextBillFetchDate', null)).format('YYYY-MM-DD HH:mm:ss');
            dataRow.status = _.get(billsData, 'commonStatus', 0);
            dataRow.extra = _.get(billsData, 'extra', null);
            dataRow.updated_at = MOMENT.utc().format('YYYY-MM-DD HH:mm:ss');
            dataRow.customerOtherInfo = _.get(billsData, 'customerOtherInfo', null);
            if(self.commonLib.isCTEventBlocked(eventName)){
                self.L.info(`Blocking CT event ${eventName}`)
                return cb()
            }
            ASYNC.waterfall([
                next => {
                    self.commonLib.getRetailerData((error) => {
                        if(error) {
                            return next(error)
                        } else {
                            return next(null)
                        }
                    }, dataRow.customer_id, dataRow);
                },
                next => {
                    self.commonLib.getCvrData((error) => {
                        if(error) {
                            return next(error)
                        } else {
                            return next(null)
                        }
                    }, productId, dataRow);
                },
                next => {                    
                    let mappedData = self.reminderUtils.createCTPipelinePayload(dataRow, eventName, dbDebugKey);
                    let clonedData = _.cloneDeep(mappedData); // needed for correct async logging
                    self.ctKafkaPublisher.publishData([{
                        topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                        messages: JSON.stringify(mappedData)
                    }], (error) => {
                        if (error) {
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:ERROR', "TYPE:CT_EVENTS", "OPERATOR:" + dataRow.operator]);
                            self.L.critical('validationSync :: publishCtEvents', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(clonedData), error);
                        } else {
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:PUBLISHED', "TYPE:CT_EVENTS", "OPERATOR:" + dataRow.operator,`EVENT_NAME:${eventName}`]);
                            self.L.log('validationSync :: publishCtEvents', 'Message published successfully in Kafka', ' on topic REMINDER_CT_EVENTS', JSON.stringify(clonedData));
                        }
                        return next(error);
                    }, [200, 800]);
                }
            ], (err) => {
                if(err)
                    self.L.log('validationSync :: publishCtEvents', 'Error while publishing message in Kafka: ', err)
                return cb(err)
            })
        });
        return;
    }

    async processCreditCardRecord(record) {
        let self = this;
        utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC_CREDIT_CARD", 'STATUS:VALID', "OPERATOR:" + record.operator]);

        let dbRecord = await self.getRecordsFromDbByCustId(record);

        if(dbRecord){
            _.set(record, 'is_automatic', _.get(dbRecord, 'is_automatic', null));
        }

        let nextBillFetchDate = self.getNextBillFetchDate(record ,_.get(record, 'is_automatic',null));
        
    //    console.log("i m heree 1536********* ", record);

        var NBFD_DAYS_TO_ADD_CC = self.billsLib.getConfigByKeys({
            dynamicConfig:false,
            name:'SUBSCRIBER_CONFIG',
            node:'NEXT_BILL_FETCH_DATES',
            keyname: record.operator,
            default:30
        },{
            currentAutomaticStatus: _.get(record, 'is_automatic', null),
            prefix:'AUTOPAY_',
            prefixToKey:'node'
        })

        var daysToBeAddedInNoBillDueCase = self.billsLib.getConfigByKeys({
            dynamicConfig:true,
            name:'CC_PUBLISHER_CONFIG',
            node:'NBFD_BASED_ON_ERRORMSG',
            keyname: 'days',
            default:7
        },{
            currentAutomaticStatus: _.get(record, 'is_automatic', null),
            prefix:'AUTOPAY_',
            prefixToKey:'node'
        })

            if(_.get(record, 'paytype', null) == 'credit card' && _.get(record, 'gateway', null) == 'euronetpostpaid' && _.get(record, 'errorMessageCode', null) == 1030) {
                if(_.get(record, 'billDate', null)) {
                    nextBillFetchDate = MOMENT(_.get(record, 'billDate', null)).add(NBFD_DAYS_TO_ADD_CC, 'days').format('YYYY-MM-DD HH:mm:ss');
                } else {
                    nextBillFetchDate = MOMENT(_.get(record, 'billFetchDate', null)).add(daysToBeAddedInNoBillDueCase, 'days').format('YYYY-MM-DD HH:mm:ss');
                }
                if(_.get(record, 'amount', 0) == 0) {
                    _.set(record, 'amount', 1000);
                }
            } else {
                if (!_.get(record, 'billDate', null)) {
                    _.set(record, 'billDate', MOMENT().format('YYYY-MM-DD'));
                }
                if(_.get(record, 'paytype', null) == 'credit card') {
                    nextBillFetchDate = MOMENT(_.get(record, 'billDate', null)).add(NBFD_DAYS_TO_ADD_CC, 'days').format('YYYY-MM-DD HH:mm:ss');
                }
            }
            _.set(record, 'nextBillFetchDate', nextBillFetchDate);


        if(MOMENT(_.get(record, 'nextBillFetchDate', null))<MOMENT() && _.get(record, 'billFetchDate', null) != null) {
            nextBillFetchDate = MOMENT(_.get(record, 'billFetchDate', null)).add(NBFD_DAYS_TO_ADD_CC, 'days').format('YYYY-MM-DD HH:mm:ss');
            _.set(record, 'nextBillFetchDate', nextBillFetchDate);
        }


        if (!dbRecord) {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC_CREDIT_CARD", 'STATUS:CREATEBILL', "OPERATOR:" + record.operator]);
            //if record not present then insert new record
            let billsData = self.getCCBillsData(record);
            record.billsData = billsData;
            let params = {
                rechargeNumber: _.get(record, 'rechargeNumber', null),
                customerId: _.get(record, 'customerId', null),
                service: _.get(record, 'service', null),
                operator: _.get(record, 'bankName', null),
                paytype: _.get(record, 'paytype', null)
            };
            let existingRecord;
            if(_.get(record, 'paytype', null) == 'credit card') {
                try {
                    existingRecord = await self.nonPaytmBillsModel.readBills(params); 
                
                    if (existingRecord.length == 1) {
                
                        let nonPaytmDueDate = _.get(existingRecord[0], 'due_date', null);
                
                        if (nonPaytmDueDate != null) {
                            _.set(record.billsData, 'dueDate', MOMENT(nonPaytmDueDate).format('YYYY-MM-DD HH:mm:ss'));
                            _.set(record.billsData, 'commonAmount', _.get(existingRecord[0], 'amount', 0));
                            let nbfd = MOMENT(_.get(existingRecord[0], 'next_bill_fetch_date', null)).format('YYYY-MM-DD HH:mm:ss');
                            if(nbfd != null) {
                                _.set(record.billsData, 'nextBillFetchDate', nbfd);
                            }
                        }
                        let nonPaytmKafkaPayload = {
                            customerId: record.customerId,
                            service: record.service,
                            paytype: record.paytype,
                            productId: record.productId,
                            operator: record.operator,
                            rechargeNumber: record.rechargeNumber,
                            dbEvent: 'delete'
                        }
                        //console.log('nonPaytmKafkaPayload 334', nonPaytmKafkaPayload);
                        await self.publishNonPaytmEvents(nonPaytmKafkaPayload);
                    } else {

                        self.L.log('processCreditCardRecord::Expected record not found');
                    }
                } catch (error) {
                    self.L.error('ValidationSync::Error during calling readbills::', error);
                }
            }
            await self.createBill(record);
        } else await self.updateAmountinCCBill(record, dbRecord);
    }

    async processPrepaidRecord(record, payLoad) {
        let self = this;
        if (record.service == 'mobile' || record.service == 'dth') {
            //console.log("processing ", record);
            let reminderFlowInstance = this.reminderFlowManager.getFlowInstance(record);
            if (!reminderFlowInstance) {
                await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record,null, null), 'could not identify flow for record');
                self.L.error(`processRecord:: could not identify flow for qParams: ${JSON.stringify(record)}`);
                return await Promise.resolve();
            }

            let nonPaytmUserFlag = await self.checkIfNewUser(record, reminderFlowInstance);

            if (!nonPaytmUserFlag) {
                let errorResponse = "Existing user for prepaid category";
                await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record, null, "RU"), errorResponse);
                return await Promise.resolve();
            } 

            record.status = _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4);
            record.customerOtherInfo = JSON.stringify(payLoad.metaData);
            record.dbEvent = 'upsert';
            record.isValaidationSync = true;

            await self.addParamsForIsValidityExpired(record);

            if (record.source) {
                let productId = await self.getProductId(record.operator, record.circle);
                if (!productId) {
                    let errorResponse = "ProductId does not exist";
                    await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record,null, "NON_RU"), errorResponse);
                    return await Promise.resolve();
                } 
                record.productId = productId;
                record.dueDate = MOMENT().format('YYYY-MM-DD HH:mm:ss');
                record.status = _.get(self.config, 'COMMON.bills_status.BILL_NOT_FOUND', 6);
            }
            await self.publishNonPaytmEvents(record);
        }
        else {
            let errorResponse = "Prepaid category does not belong to mobile or dth";
            await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record,null,null), errorResponse);
            return await Promise.resolve();
        } 
    }

    async getProductId(operator, circle) {
        return new Promise((resolve, reject) => {
            let self = this;
            if (self.operator_circle_productId_map[operator + '_' + circle]) {
                return resolve(self.operator_circle_productId_map[operator + '_' + circle])
            }
            self.digitalCatalog.get_product_list(function (e, res) {
                if (e || !res || !res.products || !res.products.length) return reject();
                let product = res.products[0];
                self.operator_circle_productId_map[operator + '_' + circle] = product.productId;
                return resolve(product.productId);
            }, operator, circle);
        });
    }

    async addParamsForIsValidityExpired(record) {
        let self = this;
        return new Promise((resolve, reject) => {
            try{
                if(record.isValidityExpired){
                    record.partialBillState = "EXPIRED";
                    record.billDate = MOMENT(record.billFetchDate).format('YYYY-MM-DD HH:mm:ss');
                    record.dueDate = null;
                    record.amount = self.OPERATOR_DEFAULT_AMOUNT[record.operator]; // DEFAULT AMOUNT
                    let extra = _.get(record, 'extra', null);
                    if(!extra || extra == 'null'){
                        extra = {};
                    }else if (typeof extra == 'string') extra = JSON.parse(extra);
                    extra.partialBillDate = record.billDate;
                    extra.partialBillState = "EXPIRED";
                    extra.isValidityExpired = record.isValidityExpired;
                    record.extra = JSON.stringify(extra);
                }
            }catch(e){
                self.L.error('addParamsForIsValidityExpired::error: Parsing Json ');
            }
            return resolve(record);
        });
    }

    async checkIfNewUser(record, reminderFlowInstance) {
        return new Promise((resolve, reject) => {
            let self = this;
            reminderFlowInstance.notificationManager_getBill(function (error, data) {
                if (error) {
                    self.L.error(`notificationManager_getBill:: getting error: ${JSON.stringify(record)} error:${error}`);
                    return reject(error);
                }
                if (data.length > 0) {
                    self.L.log(`getBill`, `Data Already presentrecords for ${record.debugKey}`);
                    return resolve();
                }
                return resolve(true);
            }, record);
        });
    }

    async publishNonPaytmEvents(record) {
        return new Promise((resolve, reject) => {
            let self = this;
            self.nonPaytmKafkaPublisher.publishData([{
                topic: _.get(self.config.KAFKA, 'SERVICES.NON_PAYTM_RECORDS.TOPIC', ''),
                messages: JSON.stringify(record)
            }], async (error) => {
                if (!error) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:PREPAID_VALIDATION_SYNC", 'STATUS:PUBLISHED', "TYPE:NON_PAYTM_EVENTS", "OPERATOR:" + record.operator]);
                    self.logger.log('nonPaytmKafkaPublisher Message published successfully in Kafka on topic NON_PAYTM_RECORDS', record, _.get(record, 'service', null));
                }
                else{
                    if(record.dbEvent !='delete'){
                        await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record, "FULL_BILL", "RU"), error,cb); 

                    }
                    //push in cassanra and dwh kafka
                }
                resolve();
            })
        });
    };


    createRecordForAnalytics(record, source_subtype_2, user_type) {
        let recordForAnalytics = {};
        recordForAnalytics.source = "validationSync";
        recordForAnalytics.source_subtype_2 = "FULL_BILL";
        recordForAnalytics.user_type = "RU";
        recordForAnalytics.customer_id = _.get(record, 'customerInfo_customer_id', null) || _.get(record, 'customerId', null) ;
        recordForAnalytics.service = _.get(record, 'productInfo_service', null) || _.get(record, 'service', null);
        recordForAnalytics.recharge_number = _.get(record, 'userData_recharge_number', null) ||  _.get(record, 'rechargeNumber', null) ;
        recordForAnalytics.operator = _.get(record, 'productInfo_operator', null)  ||  _.get(record, 'operator', null) ;
        recordForAnalytics.due_amount = _.get(record, 'dueAmount', null)|| _.get(record, 'amount', null);
        recordForAnalytics.additional_info = null;
        recordForAnalytics.sms_id = _.get(record, 'smsId', null);
        recordForAnalytics.paytype = _.get(record, 'productInfo_paytype', null) || _.get(record, 'paytype', null);
        recordForAnalytics.updated_at = _.get(record, 'updatedAt', null);
        recordForAnalytics.sender_id = _.get(record, 'smsSenderID', null);
        recordForAnalytics.sms_date_time = _.get(record, 'smsDateTime', null);
        recordForAnalytics.sms_class_id = _.get(record, 'smsClassId', null);
        recordForAnalytics.due_date = _.get(record, 'dueDate', null);
        recordForAnalytics.bill_date = _.get(record, 'billDate', null);
        recordForAnalytics.bill_fetch_date = _.get(record, 'billFetchDate',  MOMENT().format('YYYY-MM-DD HH:mm:ss'));
        return recordForAnalytics;



        
    }

    checkIfOperatorAllowedForLowbalanceElectricityPrepaid(record){
        return this.lowBalancePrepaidElectricityAllowedOperators.includes(record.operator);
    }
   
    suspendOperations(){
        var self        = this,
        deferred = Q.defer();

        self.L.log(`validationSync::suspendOperations kafka consumer shutdown initiated`);
        Q()
        .then(function(){
            self.kafkaBillFetchConsumer.close(function(error, res){
                if(error){
                    self.L.error(`validationSync::stopConsumer :: Error Stopping Consumer Err : ${error}`);
                    return error;
                }
                self.L.info(`validationSync::stopConsumer :: Consumer Stopped!  Response : ${res}`);
            })
        })
        .then(function(){
            self.L.log(`validationSync::suspendOperations kafka consumer shutdown successful`);
            deferred.resolve();
        })
        .catch(function(err){
            self.L.error(`validationSync::suspendOperations error in shutting kafka consumer`, err);
            deferred.reject(err);
        });
        return deferred.promise;
    }

    shouldIgnoreChannelId(service, channelId) {
        let self = this;
        const disabledChannels = _.get(self.disableUpmsRegChannelIdMap,[service], null);
        let ignoreChannelIdFlag = false;

        if (disabledChannels) {
          ignoreChannelIdFlag = disabledChannels.some(
            (disabledChannelId) => disabledChannelId.toLowerCase() === channelId.toLowerCase()
          );
        }

        return ignoreChannelIdFlag;
      }

    checkIfDbRecordIsEncrypted(record) {
        return _.get(record, 'is_encrypted', false);
    }


    getPrepaidFlagValue(payload) {
        const isPrepaid = _.get(payload, 'is_prepaid', "0");
        if((isPrepaid === true || isPrepaid === 'true' || isPrepaid === 1 || isPrepaid === '1'))
            return "1";
        return "0";
    }

    async handleElectricityRecord(record) {
        let self = this;
        self.L.log(`handlePrepaidElectricityRecord : record - ${JSON.stringify(record)}`);
        let response = {'proceedWithPostpaidFlow' : false};
        try {
            const postpaidTableName = record.tableName;
            let postpaidRecords = record.dbData ? record.dbData : [];
            delete record.dbData;
            record.recordFoundOfSameCustId = false;
            record.activeRecordsInDB = 0;
            self.L.log(`handleNonPrepaidFlagCase :: size of postpaid records in DB : ${postpaidRecords.length}, table - ${postpaidTableName}, fetching prepaid records`);
            record.tableName = postpaidTableName + '_prepaid';
            await self.getRecordsFromDb(record);
            let prepaidRecordList = record.dbData ? record.dbData : [];
            self.L.log(`handleElectricityRecord ~ prepaid records, recordFoundOfSameCustId : ${record.recordFoundOfSameCustId}, activeRecordsInDB : ${record.activeRecordsInDB}`);
            if (self.recentBillLibrary.checkIfIsPrepaidIsSet(record)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:PREPAID_FLAG_TRUE_ELECTRICTY']);
                self.L.log(`handleElectricityRecord :: isPrepaid flag is true`);
                self.updateRecordAsPerPrepaidFlow(record);
                self.compareAndUpdatePrepaidRecord(prepaidRecordList, record);
                self.checkAndUpdatePostpaidRecords(postpaidTableName, record);
            } else {
                self.L.log(`handleElectricityRecord :: isPrepaid flag is false`);
                if(prepaidRecordList.length > 0) {
                    self.updateRecordAsPerPrepaidFlow(record);
                    self.compareAndUpdatePrepaidRecord(prepaidRecordList, record);
                    self.checkAndUpdatePostpaidRecords(postpaidTableName, record);
                } else {
                    self.L.log(`handleNonPrepaidFlagCase :: no prepaid record exist for this recharge number : ${record.rechargeNumber}, prepaid table : ${record.tableName}, proceeding with normal postpaid flow`);
                    response.proceedWithPostpaidFlow = true;
                }
            }
        } catch (err) {
            self.L.error(`handleElectricityRecord :: Error processing record for service: ${record.service}, rechargeNumber: ${record.rechargeNumber}, operator: ${record.operator}, error: ${err}`);
            utility._sendMetricsToDD(1, [`REQUEST_TYPE:`, `SERVICE:${record.service}`, 'STATUS:ERROR', 'TYPE:PREPAID_ELECTRICITY_BILL']);
            return Promise.reject(err);
        }
        return response;
    }

    async compareAndUpdatePrepaidRecord(prepaidRecordList, record) {
        let self = this;
        let existingPrepaidCustIds = prepaidRecordList.map(prepaidRecord => prepaidRecord.customer_id);
        // Check if a new prepaid record needs to be created
        if (record.customerId && typeof record.recordFoundOfSameCustId !== "undefined" && !record.recordFoundOfSameCustId) {
            _.set(record, 'billUpdatedForSameCid', true);
            self.L.log(`validationSync ~ compareAndUpdatePrepaidRecord ~ creating new prepaid record for customerId : ${record.debugKey}`);
            await self.createBill(record);
        }
        else {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:IGNORE_PREPAID_RECORD_ALREAD_EXIST_ELECTRICTY']);
            self.L.log(`validationSync ~ compareAndUpdatePrepaidRecord ~ skipping update on existing prepaid records, customer ids : ${existingPrepaidCustIds}`);
        }
        // update existing prepaid records
    }

  async checkAndUpdatePostpaidRecords(postpaidTableName, record){
    let self=this;
    
    self.L.log(`Updating postpaid record with status = 13 and reason = PREPAID_IDENTIFIED.`);
    self.prepaidFlowManager.updatePostpaidTableWithStatus13AndReasonPrepaid(postpaidTableName, record);
  }

  updateRecordAsPerPrepaidFlow(record) {
    const self = this;
    let billsData = self.getBillsData(record, false);
    let dbRecordList = record.dbData ? record.dbData : [];
    let validRecords = dbRecordList.filter((record) => {
        const prepaidFlag = self.recentBillLibrary.checkIfIsPrepaidIsSet(record);
        return record.amount != null && prepaidFlag;
    });
    let existingRecordAmount = validRecords.length > 0 ? _.get(validRecords, '[0].amount', null) : null;

    if (!existingRecordAmount) {
        const prepaidRecord = dbRecordList.filter((record) => {
            const prepaidFlag = self.recentBillLibrary.checkIfIsPrepaidIsSet(record);
            return prepaidFlag;
        });
        if (prepaidRecord.length == 0) {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:PREPAID_RECORD_AMOUNT_NULL_ELECTRICTY', 'TYPE:FLAG_NOT_FOUND']);
        }
        else {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYNC", 'STATUS:PREPAID_RECORD_AMOUNT_NULL_ELECTRICTY', 'TYPE:AMOUNT_NULL_IN_DB']);
        }
    }

    billsData.commonDueDate = record.prePaidDueDate;
    billsData.nextBillFetchDate = record.prePaidNextBillFetchDate;
    billsData.commonAmount = existingRecordAmount;
    self.recentBillLibrary.updateIsPrepaidFlag(billsData, "1");

    record.billsData = billsData;
    record.dueDate = record.prePaidDueDate;
    record.nextBillFetchDate = record.prePaidNextBillFetchDate;
    record.amount = existingRecordAmount;
    self.recentBillLibrary.updateIsPrepaidFlag(record, "1");
  }

}


export default ValidationSync;

