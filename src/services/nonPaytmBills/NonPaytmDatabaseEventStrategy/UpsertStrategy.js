import _ from 'lodash';
import DatabaseEventBaseStrategy from './index.js';
import MOMENT from 'moment';
import PostDbOperations from '../NonPaytmPostDbOperations.js';
import BillFetchAnalytics from '../../../lib/billFetchAnalytics.js'
import AnalyticsHandler from '../AnalyticsHandler.js';
import NonPaytmBillsModel from '../../../models/nonPaytmBills.js';
import BillRecordComparator from '../nonPaytmPostProcessor/BillRecordComparator.js';
import async from 'async';
/**
 * Strategy for upserting data
 */
class UpsertStrategy extends DatabaseEventBaseStrategy {
    constructor(options) {
        super(options);
        this.cassandraCdcPublisher = options.cassandraCdcPublisher;
        this.postDbOperations = new PostDbOperations(options);
        this.billFetchAnalytics = new BillFetchAnalytics(options);
        this.analyticsHandler = new AnalyticsHandler(options);
        this.billRecordComparator = new BillRecordComparator(options);
        this.nonPaytmBillsModel = new NonPaytmBillsModel(options);
        this.config = options.config;
    }

    /**
     * Execute the upsert strategy
     * @param {Object} billsKafkaRow - Bill data
     * @returns {Promise<void>}
     */
    async execute(billsKafkaRow) {
        const self = this;
        let existingRecord = [];


        try {
            // Read all records for this recharge number
            existingRecord = await self.nonPaytmBillsModel.readBillsWithoutCustId(billsKafkaRow);
            // Filter existing records with same service and operator
            existingRecord = existingRecord.filter(record =>
                record.service === _.toLower(billsKafkaRow.service) &&
                record.operator === billsKafkaRow.operator
            );

            //log after matching the service operator
            self.L.log('execute', `Existing records found for debugKey ${_.get(billsKafkaRow, 'debugKey', null)}:`, existingRecord.length);

            //get total records to process
            let existingRecordsMap = await self.billRecordComparator.getTotalRecordsToProcess(billsKafkaRow, existingRecord);
            self.L.log('execute', `Processing records for ${existingRecordsMap.size} customer IDs`);
            // Process records with controlled concurrency
            try {
                await async.eachLimit(Array.from(existingRecordsMap.entries()), 5, async ([customerId, record], callback) => {
                    await this.processRecordWithCustomerId(billsKafkaRow, customerId, record);
                    callback();
                });
            } catch (error) {
                self.L.error('execute',
                    `Failed to process records for debugKey ${billsKafkaRow.debugKey}: ${error}`);
                throw error;
            }
            self.L.log('execute', 'Finished processing all records');
        } catch (error) {
            self.L.error('execute', `Failed to execute upsert strategy: ${error}`);
            throw error;
        }
    }

    /**
     * Process a single record with the given customer ID
     * @param {Object} billsKafkaRow - Original bill data
     * @param {string} customerId - Customer ID to process
     * @param {Object} record - Existing record if any
     * @returns {Promise<void>}
     */
    async processRecordWithCustomerId(billsKafkaRow, customerId, record) {
        const self = this;
        try {
            self.L.log('processRecordWithCustomerId', `Processing record for customer_id: ${customerId}`);
            if (record.length === 0) {
                await self.processRecordForNewRecord(billsKafkaRow);
                return;
            } else {
                let currentRecord = _.cloneDeep(billsKafkaRow);
                currentRecord.customerId = customerId;
                currentRecord.debugKey = `rech:${currentRecord.rechargeNumber}:: cust:${currentRecord.customerId}:: op:${currentRecord.operator}:: service:${currentRecord.service}:: paytype:${currentRecord.paytype}`;
                await this.processRecordForExistingRecord(currentRecord, record);
            }
        } catch (error) {
            self.L.error('processRecordWithCustomerId',
                `Failed to process record for customer_id ${customerId}: ${error.message}`);
            //need to handle analytics for this error
        }
    }


    async processRecordForNewRecord(billsKafkaRow) {
        const self = this;
        let isNewRecordToBeCreated = false;
        //check if duplicate record exists in mysql
        isNewRecordToBeCreated = self.shouldCreateNewRecord(billsKafkaRow);

        if (isNewRecordToBeCreated) {
            let updateDueTable = false, updateGenTable = false, existingRecordDueTable = [], existingRecordGenTable = [], existingRecord = [];
            try {
                await self.nonPaytmBillsModel.writeBatchRecordsNew(
                    billsKafkaRow,
                    existingRecord,
                    updateDueTable,
                    updateGenTable,
                    existingRecordDueTable,
                    existingRecordGenTable,
                    this.cassandraCdcPublisher,
                    false,
                    existingRecord
                );
            } catch (writeError) {
                self.L.error('execute', `Failed to write batch records: ${writeError.message}`);
                throw writeError;
            }
        }

        await self.postDbOperations.execute(billsKafkaRow);
    }

    /**
     * Process a single record
     * @param {Object} record - Record to process
     * @param {Object} billsKafkaRow - Bill data
     * @returns {Promise<Object>} Update results
     * @private
     */
    async processRecordForExistingRecord(billsKafkaRow, existingRecord) {
        const self = this;
        billsKafkaRow.debugKey = `rech:${existingRecord.recharge_number}:: cust:${existingRecord.customer_id}:: op:${existingRecord.operator}:: service:${existingRecord.service}:: paytype:${existingRecord.paytype}`;
        let params = self.generateParamsForReadBill(existingRecord, billsKafkaRow);
        let updateGenTable = false, updateDueTable = false, existingRecordDueTable = [], existingRecordGenTable = [], recentRecords = [];
        try {
            existingRecord = await self.nonPaytmBillsModel.readBills(params);
            self.L.log('processRecordForExistingRecord', `Existing record found for debugKey ${billsKafkaRow.debugKey}:`, existingRecord.length);
            // Post process the existing record
            billsKafkaRow = await self.billRecordComparator.process(billsKafkaRow, existingRecord);
            // Process date updates
            const updatesMonthlyTable = await this.processDateUpdates(existingRecord, billsKafkaRow);
            updateDueTable = updatesMonthlyTable.updateDueTable;
            updateGenTable = updatesMonthlyTable.updateGenTable;
            existingRecordDueTable = updatesMonthlyTable.existingRecordDueTable;
            existingRecordGenTable = updatesMonthlyTable.existingRecordGenTable;

            try {
                recentRecords = await self.nonPaytmBillsModel.readRecentBills(billsKafkaRow);
            } catch (error) {
                self.L.error('processRecordForExistingRecord', `Failed to read recent bills: ${error}`);
                throw error;
            }

            billsKafkaRow.extra = JSON.stringify(billsKafkaRow.extra)
            billsKafkaRow.customerOtherInfo = JSON.stringify(billsKafkaRow.customerOtherInfo);
            // TODO: Process extra field and customer other info for each record
            // billsKafkaRow = self.processExtraField(billsKafkaRow, partialRecordFound);
            // billsKafkaRow = self.processCustomerOtherInfo(billsKafkaRow, partialRecordFound);
            //use writeBatchRecords to write the records to the database
            try {
                await self.nonPaytmBillsModel.writeBatchRecordsNew(
                    billsKafkaRow,
                    recentRecords,
                    updateDueTable,
                    updateGenTable,
                    existingRecordDueTable,
                    existingRecordGenTable,
                    self.cassandraCdcPublisher,
                    false,
                    existingRecord
                );
            } catch (writeError) {
                self.L.error('execute', `Failed to write batch records: ${writeError.message}`);
                throw writeError;
            }
            await self.postDbOperations.execute(billsKafkaRow);
        } catch (error) {
            self.L.error('processRecordForExistingRecord', `Failed to process record: ${error}`);
            throw error;
        }
    }

    /**
     * Check if record should be updated based on date fields
     * @param {Object} record - Record to check
     * @param {string} dateField - Field name to check
     * @returns {boolean} True if record should be updated
     * @private
     */
    shouldUpdateRecord(record, dateField) {
        return record.length === 1 && _.get(record[0], dateField, null) !== null;
    }

    /**
     * Update previous due date in monthly table
     * @param {Object} existingRecord - Existing record
     * @param {Object} billsKafkaRow - New bill data
     * @returns {Promise<Object>} Result containing update flag
     * @private
     */
    async updatePrviousDueDateMonthlyTable(existingRecord, billsKafkaRow) {
        let self = this, dueGenFlag = 1, existingRecordDueTable = [];
        try {
            dueGenFlag = 1;
            existingRecordDueTable = await self.nonPaytmBillsModel.readGenAndDueBills(existingRecord, dueGenFlag);
            if (existingRecordDueTable.length > 0) {
                const currentDueDate = _.get(billsKafkaRow, 'dueDate');
                const existingDueDate = _.get(existingRecord[0], 'due_date');
                if (currentDueDate && existingDueDate && MOMENT(currentDueDate).isSame(MOMENT(existingDueDate), 'day')) {
                    self.L.log("dueTable::  existing records found");
                    updateDueTable = true;
                }
                if (updateDueTable && currentDueDate && existingDueDate && MOMENT(currentDueDate).isSame(MOMENT(existingDueDate), 'month')) {
                    updateDueTable = false;
                    await self.nonPaytmBillsModel.processRecordWithDifferentMonthDue(existingRecord[0]);
                }
                return { updateDueTable: updateDueTable, existingRecordDueTable: existingRecordDueTable };
            } else {
                self.L.error('updatePrviousDueDateMonthlyTable', `No existing records found for debugKey ${_.get(billsKafkaRow, 'debugKey', null)}`);
                throw new Error(`No existing records found in monthlyTable`);
            }

        } catch (error) {
            this.L.error('updatePrviousDueDateMonthlyTable', `Failed to update previous due date: ${error}`);
            throw error;
        }
    }

    /**
     * Update previous bill gen in monthly table
     * @param {Object} existingRecord - Existing record
     * @param {Object} billsKafkaRow - New bill data
     * @returns {Promise<Object>} Result containing update flag
     * @private
     */
    async updatePreviousBillGenMonthlyTable(existingRecord, billsKafkaRow) {
        let self = this, updateBillGenTable = false, existingRecordGenTable = [], dueGenFlag = 0;
        try {
            existingRecordGenTable = await self.nonPaytmBillsModel.readGenAndDueBills(existingRecord, dueGenFlag);
            if (existingRecordGenTable.length > 0) {
                const currentBillDate = _.get(billsKafkaRow, 'billDate');
                const existingBillDate = _.get(existingRecord[0], 'bill_date');
                if (currentBillDate && existingBillDate && MOMENT(currentBillDate).isSame(MOMENT(existingBillDate), 'day')) {
                    self.L.log("genTable::  existing records found");
                    updateBillGenTable = true;
                }
                if (updateBillGenTable && currentBillDate && existingBillDate && MOMENT(currentBillDate).isSame(MOMENT(existingBillDate), 'month')) {
                    updateBillGenTable = false;
                    await self.nonPaytmBillsModel.processRecordWithDifferentMonthGen(existingRecord[0]);
                }
                return { updateBillGenTable: updateBillGenTable, existingRecordGenTable: existingRecordGenTable };
            } else {
                self.L.error('updatePreviousBillGenMonthlyTable', `No existing records found for debugKey ${_.get(billsKafkaRow, 'debugKey', null)}`);
                throw new Error(`No existing records found in monthlyTable`);
            }
        } catch (error) {
            this.L.error('updatePrviousDueDateMonthlyTable', `Failed to update previous due date: ${error}`);
            throw error;
        }
    }

    /**
     * Process date updates for a record
     * @param {Object} existingRecord - Existing record
     * @param {Object} billsKafkaRow - New bill data
     * @returns {Promise<Object>} Update results
     * @private
     */
    async processDateUpdates(existingRecord, billsKafkaRow) {
        const updates = {
            updateDueTable: false,
            updateGenTable: false,
            existingRecordDueTable: [],
            existingRecordGenTable: []
        };

        try {
            // Process due date updates
            if (this.shouldUpdateRecord(existingRecord, 'dueDate')) {
                const dueDateResult = await this.updatePrviousDueDateMonthlyTable(existingRecord[0], billsKafkaRow);
                updates.updateDueTable = dueDateResult.updateDueTable;
                updates.existingRecordDueTable = dueDateResult.existingRecordDueTable;
            }

            // Process bill generation date updates
            if (this.shouldUpdateRecord(existingRecord, 'billDate')) {
                const genDateResult = await this.updatePreviousBillGenMonthlyTable(existingRecord[0], billsKafkaRow);
                updates.updateGenTable = genDateResult.updateGenTable;
                updates.existingRecordGenTable = genDateResult.existingRecordGenTable;
            }

            return updates;
        } catch (error) {
            this.L.error('processDateUpdates', `Failed to process date updates: ${error}`);
            throw error;
        }
    }

    /**
     * Generate parameters for reading bill records
     * @param {Object} existingRecord - Existing record from database
     * @param {Object} billsKafkaRow - New bill data from Kafka
     * @returns {Object} Parameters for reading bill records
     * @private
     */
    generateParamsForReadBill(existingRecord, billsKafkaRow) {
        return {
            rechargeNumber: _.get(existingRecord, 'recharge_number', _.get(billsKafkaRow, 'rechargeNumber')),
            customerId: _.get(existingRecord, 'customer_id', _.get(billsKafkaRow, 'customerId')),
            service: _.get(existingRecord, 'service', _.get(billsKafkaRow, 'service')),
            operator: _.get(existingRecord, 'operator', _.get(billsKafkaRow, 'operator')),
            paytype: _.get(existingRecord, 'paytype', _.get(billsKafkaRow, 'paytype')),
            isDuplicateCANumberOperator: _.get(billsKafkaRow, 'isDuplicateCANumberOperator', false),
            alternateRechargeNumber: _.get(billsKafkaRow, 'alternateRechargeNumber', null)
        };
    }

    /**
     * Check if there's a customer ID mismatch between existing and new records
     * @param {Array} existingRecord - Array of existing records
     * @param {Object} billsKafkaRow - New bill data
     * @returns {boolean} True if there's a customer ID mismatch, false otherwise
     */
    hasCustomerIdMismatch(existingRecord, billsKafkaRow) {
        const self = this;
        if (!existingRecord || existingRecord.length === 0) {
            return false;
        }

        return existingRecord.some(record => {
            const existingCustomerId = _.get(record, 'customer_id');
            const newCustomerId = _.get(billsKafkaRow, 'customerId');
            const isMismatch = existingCustomerId !== newCustomerId;

            if (isMismatch) {
                self.L.log('hasCustomerIdMismatch', `Customer ID mismatch found - Existing: ${existingCustomerId}, New: ${newCustomerId} for debugKey`, _.get(billsKafkaRow, 'debugKey', null));
            }

            return isMismatch;
        });
    }

    /**
     * Check if a new record should be created
     * @param {Object} billsKafkaRow - Bill data to check
     * @returns {boolean} True if a new record should be created, false otherwise
     */
    shouldCreateNewRecord(billsKafkaRow) {
        const self = this;
        if (billsKafkaRow.isDuplicateCANumberOperator && billsKafkaRow.alternateCAExistsInDB) {
            self.L.log('shouldCreateNewRecord', `Duplicate CA number operator found, creating new record for debugKey`, _.get(billsKafkaRow, 'debugKey', null));
            throw new Error('Duplicate record found in mysql');
        } else
            return true;
    }
}

export default UpsertStrategy;