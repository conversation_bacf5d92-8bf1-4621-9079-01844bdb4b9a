{"name": "digital-reminder", "version": "1.0.0", "private": true, "scripts": {"preinstall": "git config --global url.\"https://\".insteadOf git://", "build": "babel src -d dist --source-maps --copy-files --watch", "serve": "node dist/index.js", "githooks": "sh git-hooks/install.sh", "systematize": "npm install && npm run build && npm run test", "test": "DEBUG=true nyc --reporter=lcov --reporter=text --reporter=text-summary --reporter=cobertura mocha dist/unitTest/**/**.tests.js --exit --timeout 15000"}, "dependencies": {"@babel/runtime": "^7.27.6", "@sendgrid/mail": "^6.3.1", "async": "^2.6.0", "aws-sdk": "2.722.0", "body-parser": "^1.18.2", "cassandra-driver": "4.6.3", "cli-progress": "^3.8.1", "crypto-js": "^4.1.1", "digital-in-util": "git+ssh://*****************/paytmteam/digital-in-util.git#IN-16406", "ejs": "^2.5.7", "elasticsearch": "^14.0.0", "encrypt_decrypt": "^1.0.0", "express": "^4.16.2", "express-validator": "^6.9.2", "fast-csv": "^4.3.0", "heapdump": "^0.3.15", "infra-utils": "git+ssh://*****************:paytmteam/infra-utils.git#IN-15794_reminder", "jsonstream": "^1.0.3", "jsonwebtoken": "^8.5.1", "kafkajs": "1.12.0", "kafkajs-snappy": "^1.1.0", "lgr": "https://github.com/paytm/lgr.git#v0.0.10", "localisation-client": "git+ssh://*****************:paytmteam/localisation-client.git#v2", "lodash": "^4.17.4", "moment": "2.29.4", "newrelic": "2.3.0", "node-schedule": "^1.3.2", "nodemailer": "^0.5.15", "parquetjs-lite": "0.8.0", "paytm-statsd": "git+ssh://*****************:paytmteam/paytm-statsd.git#IN-9708", "pinpoint-node-agent": "^0.8.3", "ptmproc": "git+ssh://*****************:paytmteam/proc.git#reminder-graviton", "q": "^1.5.1", "recharge-config": "git+ssh://*****************:paytmteam/recharge-config.git#vault_new", "request": "^2.83.0", "request-promise": "^4.2.6", "rqueue": "git+ssh://*****************:paytmteam/rqueue.git#IN", "rule-engine": "git+ssh://*****************:paytmteam/digital-rule-engine.git#IN-47078", "source-map-support": "^0.5.0", "sqlwrap": "git+ssh://*****************:paytmteam/sqlwrap.git#v0.0.14", "throttled-request": "^0.1.1", "uuidv1": "^1.6.14", "validator": "^9.1.2"}, "devDependencies": {"@babel/cli": "^7.10.5", "@babel/core": "^7.10.5", "@babel/plugin-transform-runtime": "^7.10.5", "@babel/preset-env": "^7.10.4", "@babel/register": "^7.10.5", "babel-plugin-source-map-support": "^2.1.2", "chai": "~4.2.0", "chai-as-promised": "~7.1.1", "chai-http": "~2.0.1", "commander": "^2.11.0", "gensync": "^1.0.0-beta.2", "mocha": "~3.5.3", "mock-http-server": "~0.1.0", "nock": "~10.0.6", "node-mocks-http": "~1.8.0", "nyc": "^14.1.1", "proxyquire": "~2.1.3", "sinon": "~7.5.0", "sinon-chai": "3.3.0"}, "overrides": {"graceful-fs": "^4.2.11"}}